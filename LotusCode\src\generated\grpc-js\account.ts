// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: account.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { type handleServerStreamingCall, type handleUnaryCall, type UntypedServiceImplementation } from "@grpc/grpc-js";
import { Empty, EmptyRequest, Metadata, String } from "./common";

export interface AuthStateChangedRequest {
  metadata?: Metadata | undefined;
  user?: UserInfo | undefined;
}

export interface AuthStateChanged {
  user?: UserInfo | undefined;
}

export interface UserInfo {
  displayName?: string | undefined;
  email?: string | undefined;
  photoUrl?: string | undefined;
}

function createBaseAuthStateChangedRequest(): AuthStateChangedRequest {
  return { metadata: undefined, user: undefined };
}

export const AuthStateChangedRequest: MessageFns<AuthStateChangedRequest> = {
  encode(message: AuthStateChangedRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join();
    }
    if (message.user !== undefined) {
      UserInfo.encode(message.user, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AuthStateChangedRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAuthStateChangedRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.metadata = Metadata.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.user = UserInfo.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AuthStateChangedRequest {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      user: isSet(object.user) ? UserInfo.fromJSON(object.user) : undefined,
    };
  },

  toJSON(message: AuthStateChangedRequest): unknown {
    const obj: any = {};
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata);
    }
    if (message.user !== undefined) {
      obj.user = UserInfo.toJSON(message.user);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AuthStateChangedRequest>, I>>(base?: I): AuthStateChangedRequest {
    return AuthStateChangedRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuthStateChangedRequest>, I>>(object: I): AuthStateChangedRequest {
    const message = createBaseAuthStateChangedRequest();
    message.metadata = (object.metadata !== undefined && object.metadata !== null)
      ? Metadata.fromPartial(object.metadata)
      : undefined;
    message.user = (object.user !== undefined && object.user !== null) ? UserInfo.fromPartial(object.user) : undefined;
    return message;
  },
};

function createBaseAuthStateChanged(): AuthStateChanged {
  return { user: undefined };
}

export const AuthStateChanged: MessageFns<AuthStateChanged> = {
  encode(message: AuthStateChanged, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.user !== undefined) {
      UserInfo.encode(message.user, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AuthStateChanged {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAuthStateChanged();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.user = UserInfo.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AuthStateChanged {
    return { user: isSet(object.user) ? UserInfo.fromJSON(object.user) : undefined };
  },

  toJSON(message: AuthStateChanged): unknown {
    const obj: any = {};
    if (message.user !== undefined) {
      obj.user = UserInfo.toJSON(message.user);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AuthStateChanged>, I>>(base?: I): AuthStateChanged {
    return AuthStateChanged.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuthStateChanged>, I>>(object: I): AuthStateChanged {
    const message = createBaseAuthStateChanged();
    message.user = (object.user !== undefined && object.user !== null) ? UserInfo.fromPartial(object.user) : undefined;
    return message;
  },
};

function createBaseUserInfo(): UserInfo {
  return { displayName: undefined, email: undefined, photoUrl: undefined };
}

export const UserInfo: MessageFns<UserInfo> = {
  encode(message: UserInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.displayName !== undefined) {
      writer.uint32(10).string(message.displayName);
    }
    if (message.email !== undefined) {
      writer.uint32(18).string(message.email);
    }
    if (message.photoUrl !== undefined) {
      writer.uint32(26).string(message.photoUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.displayName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.photoUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserInfo {
    return {
      displayName: isSet(object.displayName) ? globalThis.String(object.displayName) : undefined,
      email: isSet(object.email) ? globalThis.String(object.email) : undefined,
      photoUrl: isSet(object.photoUrl) ? globalThis.String(object.photoUrl) : undefined,
    };
  },

  toJSON(message: UserInfo): unknown {
    const obj: any = {};
    if (message.displayName !== undefined) {
      obj.displayName = message.displayName;
    }
    if (message.email !== undefined) {
      obj.email = message.email;
    }
    if (message.photoUrl !== undefined) {
      obj.photoUrl = message.photoUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserInfo>, I>>(base?: I): UserInfo {
    return UserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInfo>, I>>(object: I): UserInfo {
    const message = createBaseUserInfo();
    message.displayName = object.displayName ?? undefined;
    message.email = object.email ?? undefined;
    message.photoUrl = object.photoUrl ?? undefined;
    return message;
  },
};

/** Service for account-related operations */
export type AccountServiceDefinition = typeof AccountServiceDefinition;
export const AccountServiceDefinition = {
  name: "AccountService",
  fullName: "cline.AccountService",
  methods: {
    /**
     * Handles the user clicking the login link in the UI.
     * Generates a secure nonce for state validation, stores it in secrets,
     * and opens the authentication URL in the external browser.
     */
    accountLoginClicked: {
      name: "accountLoginClicked",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: String,
      responseStream: false,
      options: {},
    },
    /**
     * Handles the user clicking the logout button in the UI.
     * Clears API keys and user state.
     */
    accountLogoutClicked: {
      name: "accountLogoutClicked",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {},
    },
    /** Subscribe to auth callback events (when authentication tokens are received) */
    subscribeToAuthCallback: {
      name: "subscribeToAuthCallback",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: String,
      responseStream: true,
      options: {},
    },
    /**
     * Handles authentication state changes from the Firebase context.
     * Updates the user info in global state and returns the updated value.
     */
    authStateChanged: {
      name: "authStateChanged",
      requestType: AuthStateChangedRequest,
      requestStream: false,
      responseType: AuthStateChanged,
      responseStream: false,
      options: {},
    },
  },
} as const;

/** Service for account-related operations */
export type AccountServiceService = typeof AccountServiceService;
export const AccountServiceService = {
  /**
   * Handles the user clicking the login link in the UI.
   * Generates a secure nonce for state validation, stores it in secrets,
   * and opens the authentication URL in the external browser.
   */
  accountLoginClicked: {
    path: "/cline.AccountService/accountLoginClicked",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: EmptyRequest) => Buffer.from(EmptyRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => EmptyRequest.decode(value),
    responseSerialize: (value: String) => Buffer.from(String.encode(value).finish()),
    responseDeserialize: (value: Buffer) => String.decode(value),
  },
  /**
   * Handles the user clicking the logout button in the UI.
   * Clears API keys and user state.
   */
  accountLogoutClicked: {
    path: "/cline.AccountService/accountLogoutClicked",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: EmptyRequest) => Buffer.from(EmptyRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => EmptyRequest.decode(value),
    responseSerialize: (value: Empty) => Buffer.from(Empty.encode(value).finish()),
    responseDeserialize: (value: Buffer) => Empty.decode(value),
  },
  /** Subscribe to auth callback events (when authentication tokens are received) */
  subscribeToAuthCallback: {
    path: "/cline.AccountService/subscribeToAuthCallback",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: EmptyRequest) => Buffer.from(EmptyRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => EmptyRequest.decode(value),
    responseSerialize: (value: String) => Buffer.from(String.encode(value).finish()),
    responseDeserialize: (value: Buffer) => String.decode(value),
  },
  /**
   * Handles authentication state changes from the Firebase context.
   * Updates the user info in global state and returns the updated value.
   */
  authStateChanged: {
    path: "/cline.AccountService/authStateChanged",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: AuthStateChangedRequest) => Buffer.from(AuthStateChangedRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => AuthStateChangedRequest.decode(value),
    responseSerialize: (value: AuthStateChanged) => Buffer.from(AuthStateChanged.encode(value).finish()),
    responseDeserialize: (value: Buffer) => AuthStateChanged.decode(value),
  },
} as const;

export interface AccountServiceServer extends UntypedServiceImplementation {
  /**
   * Handles the user clicking the login link in the UI.
   * Generates a secure nonce for state validation, stores it in secrets,
   * and opens the authentication URL in the external browser.
   */
  accountLoginClicked: handleUnaryCall<EmptyRequest, String>;
  /**
   * Handles the user clicking the logout button in the UI.
   * Clears API keys and user state.
   */
  accountLogoutClicked: handleUnaryCall<EmptyRequest, Empty>;
  /** Subscribe to auth callback events (when authentication tokens are received) */
  subscribeToAuthCallback: handleServerStreamingCall<EmptyRequest, String>;
  /**
   * Handles authentication state changes from the Firebase context.
   * Updates the user info in global state and returns the updated value.
   */
  authStateChanged: handleUnaryCall<AuthStateChangedRequest, AuthStateChanged>;
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
