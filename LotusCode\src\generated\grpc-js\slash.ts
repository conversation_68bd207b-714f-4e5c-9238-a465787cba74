// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: slash.proto

/* eslint-disable */
import { type handleUnaryCall, type UntypedServiceImplementation } from "@grpc/grpc-js";
import { Empty, StringRequest } from "./common";

/** SlashService provides methods for managing slash */
export type SlashServiceDefinition = typeof SlashServiceDefinition;
export const SlashServiceDefinition = {
  name: "SlashService",
  fullName: "cline.SlashService",
  methods: {
    /** Sends button click message */
    reportBug: {
      name: "reportBug",
      requestType: StringRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {},
    },
    condense: {
      name: "condense",
      requestType: StringRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {},
    },
  },
} as const;

/** SlashService provides methods for managing slash */
export type SlashServiceService = typeof SlashServiceService;
export const SlashServiceService = {
  /** Sends button click message */
  reportBug: {
    path: "/cline.SlashService/reportBug",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: StringRequest) => Buffer.from(StringRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => StringRequest.decode(value),
    responseSerialize: (value: Empty) => Buffer.from(Empty.encode(value).finish()),
    responseDeserialize: (value: Buffer) => Empty.decode(value),
  },
  condense: {
    path: "/cline.SlashService/condense",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: StringRequest) => Buffer.from(StringRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => StringRequest.decode(value),
    responseSerialize: (value: Empty) => Buffer.from(Empty.encode(value).finish()),
    responseDeserialize: (value: Buffer) => Empty.decode(value),
  },
} as const;

export interface SlashServiceServer extends UntypedServiceImplementation {
  /** Sends button click message */
  reportBug: handleUnaryCall<StringRequest, Empty>;
  condense: handleUnaryCall<StringRequest, Empty>;
}
