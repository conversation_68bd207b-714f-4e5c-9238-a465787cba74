---
title: "Anthropic"
description: "Learn how to configure and use Anthropic Claude models with <PERSON><PERSON>. Covers API key setup, model selection, and advanced features like prompt caching."
---

**Website:** [https://www.anthropic.com/](https://www.anthropic.com/)

### Getting an API Key

1.  **Sign Up/Sign In:** Go to the [Anthropic Console](https://console.anthropic.com/). Create an account or sign in.
2.  **Navigate to API Keys:** Go to the [API keys](https://console.anthropic.com/settings/keys) section.
3.  **Create a Key:** Click "Create Key". Give your key a descriptive name (e.g., "<PERSON><PERSON>").
4.  **Copy the Key:** **Important:** Copy the API key _immediately_. You will not be able to see it again. Store it securely.

### Supported Models

Cline supports the following Anthropic Claude models:

-   `claude-opus-4-********`
-   `claude-opus-4-********:thinking` (Extended Thinking variant)
-   `claude-sonnet-4-********` (Recommended)
-   `claude-sonnet-4-********:thinking` (Extended Thinking variant)
-   `claude-3-7-sonnet-********`
-   `claude-3-7-sonnet-********:thinking` (Extended Thinking variant)
-   `claude-3-5-sonnet-********`
-   `claude-3-5-haiku-********`
-   `claude-3-opus-********`
-   `claude-3-haiku-********`

See [Anthropic's Model Documentation](https://docs.anthropic.com/en/docs/about-claude/models) for more details on each model's capabilities.

### Configuration in Cline

1.  **Open Cline Settings:** Click the settings icon (⚙️) in the Cline panel.
2.  **Select Provider:** Choose "Anthropic" from the "API Provider" dropdown.
3.  **Enter API Key:** Paste your Anthropic API key into the "Anthropic API Key" field.
4.  **Select Model:** Choose your desired Claude model from the "Model" dropdown.
5.  **(Optional) Custom Base URL:** If you need to use a custom base URL for the Anthropic API, check "Use custom base URL" and enter the URL. Most users won't need to adjust this setting.

### Extended Thinking

Anthropic models offer an "Extended Thinking" feature, designed to give them enhanced reasoning capabilities for complex tasks. This feature allows the model to output its step-by-step thought process before delivering a final answer, providing transparency and enabling more thorough analysis for challenging prompts.

When extended thinking is in Cline, the model generates `thinking` content blocks that detail its internal reasoning. These insights are then incorporated into its final response.
Cline users can leverage this by checking the `Enable Extended Thinking` box below the model selection menu after selecting a Claude Model from any provider.

**Key Aspects of Extended Thinking:**

-   **Supported Models:** This feature is available for select models, including variants of Claude Opus 4, Claude Sonnet 4, and Claude Sonnet 3.7. The specific models listed in the "Supported Models" section above with the `:thinking` suffix are pre-configured in Cline to utilize this.
-   **Summarized Thinking (Claude 4):** For Claude 4 models, the API returns a summary of the full thinking process to balance insight with efficiency and prevent misuse. You are billed for the full thinking tokens, not just the summary.
-   **Streaming:** Extended thinking responses, including the `thinking` blocks, can be streamed.
-   **Tool Use & Prompt Caching:** Extended thinking interacts with tool use (requiring thinking blocks to be passed back) and prompt caching (with specific behaviors around cache invalidation and context).

For comprehensive details on how extended thinking works, including API examples, interaction with tool use, prompt caching, and pricing, please refer to the [official Anthropic documentation on Extended Thinking](https://docs.anthropic.com/en/docs/build-with-claude/extended-thinking).

### Tips and Notes

-   **Prompt Caching:** Claude 3 models support [prompt caching](https://docs.anthropic.com/en/docs/build-with-claude/prompt-caching), which can significantly reduce costs and latency for repeated prompts.
-   **Context Window:** Claude models have large context windows (200,000 tokens), allowing you to include a significant amount of code and context in your prompts.
-   **Pricing:** Refer to the [Anthropic Pricing](https://www.anthropic.com/pricing) page for the latest pricing information.
-   **Rate Limits:** Anthropic has strict rate limits based on [usage tiers](https://docs.anthropic.com/en/api/rate-limits#requirements-to-advance-tier). If you're repeatedly hitting rate limits, consider contacting Anthropic sales or accessing Claude through a different provider like [OpenRouter](/provider-config/openrouter) or [Requesty](/provider-config/requesty).
