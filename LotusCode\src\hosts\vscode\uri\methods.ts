// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js

// Import all method implementations
import { registerMethod } from "./index"
import { file } from "./file"
import { joinPath } from "./joinPath"
import { parse } from "./parse"

// Register all uri service methods
export function registerAllMethods(): void {
	// Register each method with the registry
	registerMethod("file", file)
	registerMethod("joinPath", joinPath)
	registerMethod("parse", parse)
}