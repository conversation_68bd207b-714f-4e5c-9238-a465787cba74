// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: host/uri.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { type handleUnaryCall, type UntypedServiceImplementation } from "@grpc/grpc-js";
import { Metadata, StringRequest } from "../common";

/** <PERSON><PERSON> represents a URI in the IDE */
export interface Uri {
  scheme: string;
  authority: string;
  path: string;
  query: string;
  fragment: string;
  fsPath: string;
}

/** Request for joining path segments to a URI */
export interface JoinPathRequest {
  metadata?: Metadata | undefined;
  base?: Uri | undefined;
  pathSegments: string[];
}

function createBaseUri(): Uri {
  return { scheme: "", authority: "", path: "", query: "", fragment: "", fsPath: "" };
}

export const Uri: MessageFns<Uri> = {
  encode(message: U<PERSON>, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.scheme !== "") {
      writer.uint32(10).string(message.scheme);
    }
    if (message.authority !== "") {
      writer.uint32(18).string(message.authority);
    }
    if (message.path !== "") {
      writer.uint32(26).string(message.path);
    }
    if (message.query !== "") {
      writer.uint32(34).string(message.query);
    }
    if (message.fragment !== "") {
      writer.uint32(42).string(message.fragment);
    }
    if (message.fsPath !== "") {
      writer.uint32(50).string(message.fsPath);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Uri {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUri();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.scheme = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.authority = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.path = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.query = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.fragment = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.fsPath = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Uri {
    return {
      scheme: isSet(object.scheme) ? globalThis.String(object.scheme) : "",
      authority: isSet(object.authority) ? globalThis.String(object.authority) : "",
      path: isSet(object.path) ? globalThis.String(object.path) : "",
      query: isSet(object.query) ? globalThis.String(object.query) : "",
      fragment: isSet(object.fragment) ? globalThis.String(object.fragment) : "",
      fsPath: isSet(object.fsPath) ? globalThis.String(object.fsPath) : "",
    };
  },

  toJSON(message: Uri): unknown {
    const obj: any = {};
    if (message.scheme !== "") {
      obj.scheme = message.scheme;
    }
    if (message.authority !== "") {
      obj.authority = message.authority;
    }
    if (message.path !== "") {
      obj.path = message.path;
    }
    if (message.query !== "") {
      obj.query = message.query;
    }
    if (message.fragment !== "") {
      obj.fragment = message.fragment;
    }
    if (message.fsPath !== "") {
      obj.fsPath = message.fsPath;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Uri>, I>>(base?: I): Uri {
    return Uri.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Uri>, I>>(object: I): Uri {
    const message = createBaseUri();
    message.scheme = object.scheme ?? "";
    message.authority = object.authority ?? "";
    message.path = object.path ?? "";
    message.query = object.query ?? "";
    message.fragment = object.fragment ?? "";
    message.fsPath = object.fsPath ?? "";
    return message;
  },
};

function createBaseJoinPathRequest(): JoinPathRequest {
  return { metadata: undefined, base: undefined, pathSegments: [] };
}

export const JoinPathRequest: MessageFns<JoinPathRequest> = {
  encode(message: JoinPathRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join();
    }
    if (message.base !== undefined) {
      Uri.encode(message.base, writer.uint32(18).fork()).join();
    }
    for (const v of message.pathSegments) {
      writer.uint32(26).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): JoinPathRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseJoinPathRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.metadata = Metadata.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.base = Uri.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pathSegments.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): JoinPathRequest {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      base: isSet(object.base) ? Uri.fromJSON(object.base) : undefined,
      pathSegments: globalThis.Array.isArray(object?.pathSegments)
        ? object.pathSegments.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: JoinPathRequest): unknown {
    const obj: any = {};
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata);
    }
    if (message.base !== undefined) {
      obj.base = Uri.toJSON(message.base);
    }
    if (message.pathSegments?.length) {
      obj.pathSegments = message.pathSegments;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<JoinPathRequest>, I>>(base?: I): JoinPathRequest {
    return JoinPathRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<JoinPathRequest>, I>>(object: I): JoinPathRequest {
    const message = createBaseJoinPathRequest();
    message.metadata = (object.metadata !== undefined && object.metadata !== null)
      ? Metadata.fromPartial(object.metadata)
      : undefined;
    message.base = (object.base !== undefined && object.base !== null) ? Uri.fromPartial(object.base) : undefined;
    message.pathSegments = object.pathSegments?.map((e) => e) || [];
    return message;
  },
};

/** UriService provides methods for working with URIs in the IDE */
export type UriServiceDefinition = typeof UriServiceDefinition;
export const UriServiceDefinition = {
  name: "UriService",
  fullName: "host.UriService",
  methods: {
    /** Create a new file URI from a file path */
    file: {
      name: "file",
      requestType: StringRequest,
      requestStream: false,
      responseType: Uri,
      responseStream: false,
      options: {},
    },
    /** Join a URI with additional path segments */
    joinPath: {
      name: "joinPath",
      requestType: JoinPathRequest,
      requestStream: false,
      responseType: Uri,
      responseStream: false,
      options: {},
    },
    /** Parse a string URI into a Uri object */
    parse: {
      name: "parse",
      requestType: StringRequest,
      requestStream: false,
      responseType: Uri,
      responseStream: false,
      options: {},
    },
  },
} as const;

/** UriService provides methods for working with URIs in the IDE */
export type UriServiceService = typeof UriServiceService;
export const UriServiceService = {
  /** Create a new file URI from a file path */
  file: {
    path: "/host.UriService/file",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: StringRequest) => Buffer.from(StringRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => StringRequest.decode(value),
    responseSerialize: (value: Uri) => Buffer.from(Uri.encode(value).finish()),
    responseDeserialize: (value: Buffer) => Uri.decode(value),
  },
  /** Join a URI with additional path segments */
  joinPath: {
    path: "/host.UriService/joinPath",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: JoinPathRequest) => Buffer.from(JoinPathRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => JoinPathRequest.decode(value),
    responseSerialize: (value: Uri) => Buffer.from(Uri.encode(value).finish()),
    responseDeserialize: (value: Buffer) => Uri.decode(value),
  },
  /** Parse a string URI into a Uri object */
  parse: {
    path: "/host.UriService/parse",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: StringRequest) => Buffer.from(StringRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => StringRequest.decode(value),
    responseSerialize: (value: Uri) => Buffer.from(Uri.encode(value).finish()),
    responseDeserialize: (value: Buffer) => Uri.decode(value),
  },
} as const;

export interface UriServiceServer extends UntypedServiceImplementation {
  /** Create a new file URI from a file path */
  file: handleUnaryCall<StringRequest, Uri>;
  /** Join a URI with additional path segments */
  joinPath: handleUnaryCall<JoinPathRequest, Uri>;
  /** Parse a string URI into a Uri object */
  parse: handleUnaryCall<StringRequest, Uri>;
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
