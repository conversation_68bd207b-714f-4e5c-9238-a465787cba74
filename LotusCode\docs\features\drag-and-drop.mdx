---
title: "Drag & Drop"
sidebarTitle: "Drag & Drop"
---

Dragging and dropping files into Cline is a quick way to add images, code, and other files to your conversations.

<Note>Due to VS Code quirks, to drag and drop files into the Cline chat input, you need to hold `Shift` while dragging.</Note>

Dragging and dropping workspace files into Cline will automatically create a [file mention](/features/at-mentions/file-mentions). This allows you to reference the file in your conversation without needing to type out the path.

### Supported File Types

Cline supports dragging external images from your file system, as well as files from your workspace.
