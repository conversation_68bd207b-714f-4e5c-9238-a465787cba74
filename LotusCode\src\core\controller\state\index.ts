// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js

import { createServiceRegistry, ServiceMethodHandler, StreamingMethodHandler } from "../grpc-service"
import { StreamingResponseHandler } from "../grpc-handler"
import { registerAllMethods } from "./methods"

// Create state service registry
const stateService = createServiceRegistry("state")

// Export the method handler types and registration function
export type StateMethodHandler = ServiceMethodHandler
export type StateStreamingMethodHandler = StreamingMethodHandler
export const registerMethod = stateService.registerMethod

// Export the request handlers
export const handleStateServiceRequest = stateService.handleRequest
export const handleStateServiceStreamingRequest = stateService.handleStreamingRequest
export const isStreamingMethod = stateService.isStreamingMethod

// Register all state methods
registerAllMethods()