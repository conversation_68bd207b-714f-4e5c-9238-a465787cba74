// GENERATED CODE -- DO NOT EDIT!
// Generated by scripts/generate-host-bridge-client.mjs
import { asyncIteratorToCallbacks } from "@/standalone/utils"
import * as niceGrpc from "@generated/nice-grpc/index"
import { StreamingCallbacks } from "@hosts/host-provider-types"
import * as proto from "@shared/proto/index"
import { Channel, createClient } from "nice-grpc"

import { UriServiceClientInterface } from "@generated/hosts/host-bridge-client-types"
import { WatchServiceClientInterface } from "@generated/hosts/host-bridge-client-types"

/**
 * Type-safe client implementation for UriService.
 */
export class UriServiceClientImpl implements UriServiceClientInterface {
  private client: niceGrpc.host.UriServiceClient 

  constructor(channel: Channel) {
    this.client = createClient(niceGrpc.host.UriServiceDefinition, channel)
  }

  file(request: proto.cline.StringRequest): Promise<proto.host.Uri> {
    return this.client.file(request)
  }

  joinPath(request: proto.host.JoinPathRequest): Promise<proto.host.Uri> {
    return this.client.joinPath(request)
  }

  parse(request: proto.cline.StringRequest): Promise<proto.host.Uri> {
    return this.client.parse(request)
  }
}

/**
 * Type-safe client implementation for WatchService.
 */
export class WatchServiceClientImpl implements WatchServiceClientInterface {
  private client: niceGrpc.host.WatchServiceClient 

  constructor(channel: Channel) {
    this.client = createClient(niceGrpc.host.WatchServiceDefinition, channel)
  }

  subscribeToFile(request: proto.host.SubscribeToFileRequest, callbacks: StreamingCallbacks<proto.host.FileChangeEvent>): () => void {
	const abortController = new AbortController()
	const stream: AsyncIterable<proto.host.FileChangeEvent> = this.client.subscribeToFile(request, {signal: abortController.signal})
    asyncIteratorToCallbacks(stream, callbacks)
	return () => {abortController.abort()}
  }
}
