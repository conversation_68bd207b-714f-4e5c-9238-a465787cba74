// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: ui.proto

/* eslint-disable */
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Boolean, Empty, EmptyRequest, KeyValuePair, Metadata, String, StringRequest } from "./common";

/** Enum for webview provider types */
export enum WebviewProviderType {
  SIDEBAR = 0,
  TAB = 1,
  UNRECOGNIZED = -1,
}

export function webviewProviderTypeFromJSON(object: any): WebviewProviderType {
  switch (object) {
    case 0:
    case "SIDEBAR":
      return WebviewProviderType.SIDEBAR;
    case 1:
    case "TAB":
      return WebviewProviderType.TAB;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WebviewProviderType.UNRECOGNIZED;
  }
}

export function webviewProviderTypeToJSON(object: WebviewProviderType): string {
  switch (object) {
    case WebviewProviderType.SIDEBAR:
      return "SIDEBAR";
    case WebviewProviderType.TAB:
      return "TAB";
    case WebviewProviderType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** Enum for ClineMessage type */
export enum ClineMessageType {
  ASK = 0,
  SAY = 1,
  UNRECOGNIZED = -1,
}

export function clineMessageTypeFromJSON(object: any): ClineMessageType {
  switch (object) {
    case 0:
    case "ASK":
      return ClineMessageType.ASK;
    case 1:
    case "SAY":
      return ClineMessageType.SAY;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ClineMessageType.UNRECOGNIZED;
  }
}

export function clineMessageTypeToJSON(object: ClineMessageType): string {
  switch (object) {
    case ClineMessageType.ASK:
      return "ASK";
    case ClineMessageType.SAY:
      return "SAY";
    case ClineMessageType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** Enum for ClineAsk types */
export enum ClineAsk {
  FOLLOWUP = 0,
  PLAN_MODE_RESPOND = 1,
  COMMAND = 2,
  COMMAND_OUTPUT = 3,
  COMPLETION_RESULT = 4,
  TOOL = 5,
  API_REQ_FAILED = 6,
  RESUME_TASK = 7,
  RESUME_COMPLETED_TASK = 8,
  MISTAKE_LIMIT_REACHED = 9,
  AUTO_APPROVAL_MAX_REQ_REACHED = 10,
  BROWSER_ACTION_LAUNCH = 11,
  USE_MCP_SERVER = 12,
  NEW_TASK = 13,
  CONDENSE = 14,
  REPORT_BUG = 15,
  UNRECOGNIZED = -1,
}

export function clineAskFromJSON(object: any): ClineAsk {
  switch (object) {
    case 0:
    case "FOLLOWUP":
      return ClineAsk.FOLLOWUP;
    case 1:
    case "PLAN_MODE_RESPOND":
      return ClineAsk.PLAN_MODE_RESPOND;
    case 2:
    case "COMMAND":
      return ClineAsk.COMMAND;
    case 3:
    case "COMMAND_OUTPUT":
      return ClineAsk.COMMAND_OUTPUT;
    case 4:
    case "COMPLETION_RESULT":
      return ClineAsk.COMPLETION_RESULT;
    case 5:
    case "TOOL":
      return ClineAsk.TOOL;
    case 6:
    case "API_REQ_FAILED":
      return ClineAsk.API_REQ_FAILED;
    case 7:
    case "RESUME_TASK":
      return ClineAsk.RESUME_TASK;
    case 8:
    case "RESUME_COMPLETED_TASK":
      return ClineAsk.RESUME_COMPLETED_TASK;
    case 9:
    case "MISTAKE_LIMIT_REACHED":
      return ClineAsk.MISTAKE_LIMIT_REACHED;
    case 10:
    case "AUTO_APPROVAL_MAX_REQ_REACHED":
      return ClineAsk.AUTO_APPROVAL_MAX_REQ_REACHED;
    case 11:
    case "BROWSER_ACTION_LAUNCH":
      return ClineAsk.BROWSER_ACTION_LAUNCH;
    case 12:
    case "USE_MCP_SERVER":
      return ClineAsk.USE_MCP_SERVER;
    case 13:
    case "NEW_TASK":
      return ClineAsk.NEW_TASK;
    case 14:
    case "CONDENSE":
      return ClineAsk.CONDENSE;
    case 15:
    case "REPORT_BUG":
      return ClineAsk.REPORT_BUG;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ClineAsk.UNRECOGNIZED;
  }
}

export function clineAskToJSON(object: ClineAsk): string {
  switch (object) {
    case ClineAsk.FOLLOWUP:
      return "FOLLOWUP";
    case ClineAsk.PLAN_MODE_RESPOND:
      return "PLAN_MODE_RESPOND";
    case ClineAsk.COMMAND:
      return "COMMAND";
    case ClineAsk.COMMAND_OUTPUT:
      return "COMMAND_OUTPUT";
    case ClineAsk.COMPLETION_RESULT:
      return "COMPLETION_RESULT";
    case ClineAsk.TOOL:
      return "TOOL";
    case ClineAsk.API_REQ_FAILED:
      return "API_REQ_FAILED";
    case ClineAsk.RESUME_TASK:
      return "RESUME_TASK";
    case ClineAsk.RESUME_COMPLETED_TASK:
      return "RESUME_COMPLETED_TASK";
    case ClineAsk.MISTAKE_LIMIT_REACHED:
      return "MISTAKE_LIMIT_REACHED";
    case ClineAsk.AUTO_APPROVAL_MAX_REQ_REACHED:
      return "AUTO_APPROVAL_MAX_REQ_REACHED";
    case ClineAsk.BROWSER_ACTION_LAUNCH:
      return "BROWSER_ACTION_LAUNCH";
    case ClineAsk.USE_MCP_SERVER:
      return "USE_MCP_SERVER";
    case ClineAsk.NEW_TASK:
      return "NEW_TASK";
    case ClineAsk.CONDENSE:
      return "CONDENSE";
    case ClineAsk.REPORT_BUG:
      return "REPORT_BUG";
    case ClineAsk.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** Enum for ClineSay types */
export enum ClineSay {
  TASK = 0,
  ERROR = 1,
  API_REQ_STARTED = 2,
  API_REQ_FINISHED = 3,
  TEXT = 4,
  REASONING = 5,
  COMPLETION_RESULT_SAY = 6,
  USER_FEEDBACK = 7,
  USER_FEEDBACK_DIFF = 8,
  API_REQ_RETRIED = 9,
  COMMAND_SAY = 10,
  COMMAND_OUTPUT_SAY = 11,
  TOOL_SAY = 12,
  SHELL_INTEGRATION_WARNING = 13,
  BROWSER_ACTION_LAUNCH_SAY = 14,
  BROWSER_ACTION = 15,
  BROWSER_ACTION_RESULT = 16,
  MCP_SERVER_REQUEST_STARTED = 17,
  MCP_SERVER_RESPONSE = 18,
  MCP_NOTIFICATION = 19,
  USE_MCP_SERVER_SAY = 20,
  DIFF_ERROR = 21,
  DELETED_API_REQS = 22,
  CLINEIGNORE_ERROR = 23,
  CHECKPOINT_CREATED = 24,
  LOAD_MCP_DOCUMENTATION = 25,
  INFO = 26,
  UNRECOGNIZED = -1,
}

export function clineSayFromJSON(object: any): ClineSay {
  switch (object) {
    case 0:
    case "TASK":
      return ClineSay.TASK;
    case 1:
    case "ERROR":
      return ClineSay.ERROR;
    case 2:
    case "API_REQ_STARTED":
      return ClineSay.API_REQ_STARTED;
    case 3:
    case "API_REQ_FINISHED":
      return ClineSay.API_REQ_FINISHED;
    case 4:
    case "TEXT":
      return ClineSay.TEXT;
    case 5:
    case "REASONING":
      return ClineSay.REASONING;
    case 6:
    case "COMPLETION_RESULT_SAY":
      return ClineSay.COMPLETION_RESULT_SAY;
    case 7:
    case "USER_FEEDBACK":
      return ClineSay.USER_FEEDBACK;
    case 8:
    case "USER_FEEDBACK_DIFF":
      return ClineSay.USER_FEEDBACK_DIFF;
    case 9:
    case "API_REQ_RETRIED":
      return ClineSay.API_REQ_RETRIED;
    case 10:
    case "COMMAND_SAY":
      return ClineSay.COMMAND_SAY;
    case 11:
    case "COMMAND_OUTPUT_SAY":
      return ClineSay.COMMAND_OUTPUT_SAY;
    case 12:
    case "TOOL_SAY":
      return ClineSay.TOOL_SAY;
    case 13:
    case "SHELL_INTEGRATION_WARNING":
      return ClineSay.SHELL_INTEGRATION_WARNING;
    case 14:
    case "BROWSER_ACTION_LAUNCH_SAY":
      return ClineSay.BROWSER_ACTION_LAUNCH_SAY;
    case 15:
    case "BROWSER_ACTION":
      return ClineSay.BROWSER_ACTION;
    case 16:
    case "BROWSER_ACTION_RESULT":
      return ClineSay.BROWSER_ACTION_RESULT;
    case 17:
    case "MCP_SERVER_REQUEST_STARTED":
      return ClineSay.MCP_SERVER_REQUEST_STARTED;
    case 18:
    case "MCP_SERVER_RESPONSE":
      return ClineSay.MCP_SERVER_RESPONSE;
    case 19:
    case "MCP_NOTIFICATION":
      return ClineSay.MCP_NOTIFICATION;
    case 20:
    case "USE_MCP_SERVER_SAY":
      return ClineSay.USE_MCP_SERVER_SAY;
    case 21:
    case "DIFF_ERROR":
      return ClineSay.DIFF_ERROR;
    case 22:
    case "DELETED_API_REQS":
      return ClineSay.DELETED_API_REQS;
    case 23:
    case "CLINEIGNORE_ERROR":
      return ClineSay.CLINEIGNORE_ERROR;
    case 24:
    case "CHECKPOINT_CREATED":
      return ClineSay.CHECKPOINT_CREATED;
    case 25:
    case "LOAD_MCP_DOCUMENTATION":
      return ClineSay.LOAD_MCP_DOCUMENTATION;
    case 26:
    case "INFO":
      return ClineSay.INFO;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ClineSay.UNRECOGNIZED;
  }
}

export function clineSayToJSON(object: ClineSay): string {
  switch (object) {
    case ClineSay.TASK:
      return "TASK";
    case ClineSay.ERROR:
      return "ERROR";
    case ClineSay.API_REQ_STARTED:
      return "API_REQ_STARTED";
    case ClineSay.API_REQ_FINISHED:
      return "API_REQ_FINISHED";
    case ClineSay.TEXT:
      return "TEXT";
    case ClineSay.REASONING:
      return "REASONING";
    case ClineSay.COMPLETION_RESULT_SAY:
      return "COMPLETION_RESULT_SAY";
    case ClineSay.USER_FEEDBACK:
      return "USER_FEEDBACK";
    case ClineSay.USER_FEEDBACK_DIFF:
      return "USER_FEEDBACK_DIFF";
    case ClineSay.API_REQ_RETRIED:
      return "API_REQ_RETRIED";
    case ClineSay.COMMAND_SAY:
      return "COMMAND_SAY";
    case ClineSay.COMMAND_OUTPUT_SAY:
      return "COMMAND_OUTPUT_SAY";
    case ClineSay.TOOL_SAY:
      return "TOOL_SAY";
    case ClineSay.SHELL_INTEGRATION_WARNING:
      return "SHELL_INTEGRATION_WARNING";
    case ClineSay.BROWSER_ACTION_LAUNCH_SAY:
      return "BROWSER_ACTION_LAUNCH_SAY";
    case ClineSay.BROWSER_ACTION:
      return "BROWSER_ACTION";
    case ClineSay.BROWSER_ACTION_RESULT:
      return "BROWSER_ACTION_RESULT";
    case ClineSay.MCP_SERVER_REQUEST_STARTED:
      return "MCP_SERVER_REQUEST_STARTED";
    case ClineSay.MCP_SERVER_RESPONSE:
      return "MCP_SERVER_RESPONSE";
    case ClineSay.MCP_NOTIFICATION:
      return "MCP_NOTIFICATION";
    case ClineSay.USE_MCP_SERVER_SAY:
      return "USE_MCP_SERVER_SAY";
    case ClineSay.DIFF_ERROR:
      return "DIFF_ERROR";
    case ClineSay.DELETED_API_REQS:
      return "DELETED_API_REQS";
    case ClineSay.CLINEIGNORE_ERROR:
      return "CLINEIGNORE_ERROR";
    case ClineSay.CHECKPOINT_CREATED:
      return "CHECKPOINT_CREATED";
    case ClineSay.LOAD_MCP_DOCUMENTATION:
      return "LOAD_MCP_DOCUMENTATION";
    case ClineSay.INFO:
      return "INFO";
    case ClineSay.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** Enum for ClineSayTool tool types */
export enum ClineSayToolType {
  EDITED_EXISTING_FILE = 0,
  NEW_FILE_CREATED = 1,
  READ_FILE = 2,
  LIST_FILES_TOP_LEVEL = 3,
  LIST_FILES_RECURSIVE = 4,
  LIST_CODE_DEFINITION_NAMES = 5,
  SEARCH_FILES = 6,
  WEB_FETCH = 7,
  UNRECOGNIZED = -1,
}

export function clineSayToolTypeFromJSON(object: any): ClineSayToolType {
  switch (object) {
    case 0:
    case "EDITED_EXISTING_FILE":
      return ClineSayToolType.EDITED_EXISTING_FILE;
    case 1:
    case "NEW_FILE_CREATED":
      return ClineSayToolType.NEW_FILE_CREATED;
    case 2:
    case "READ_FILE":
      return ClineSayToolType.READ_FILE;
    case 3:
    case "LIST_FILES_TOP_LEVEL":
      return ClineSayToolType.LIST_FILES_TOP_LEVEL;
    case 4:
    case "LIST_FILES_RECURSIVE":
      return ClineSayToolType.LIST_FILES_RECURSIVE;
    case 5:
    case "LIST_CODE_DEFINITION_NAMES":
      return ClineSayToolType.LIST_CODE_DEFINITION_NAMES;
    case 6:
    case "SEARCH_FILES":
      return ClineSayToolType.SEARCH_FILES;
    case 7:
    case "WEB_FETCH":
      return ClineSayToolType.WEB_FETCH;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ClineSayToolType.UNRECOGNIZED;
  }
}

export function clineSayToolTypeToJSON(object: ClineSayToolType): string {
  switch (object) {
    case ClineSayToolType.EDITED_EXISTING_FILE:
      return "EDITED_EXISTING_FILE";
    case ClineSayToolType.NEW_FILE_CREATED:
      return "NEW_FILE_CREATED";
    case ClineSayToolType.READ_FILE:
      return "READ_FILE";
    case ClineSayToolType.LIST_FILES_TOP_LEVEL:
      return "LIST_FILES_TOP_LEVEL";
    case ClineSayToolType.LIST_FILES_RECURSIVE:
      return "LIST_FILES_RECURSIVE";
    case ClineSayToolType.LIST_CODE_DEFINITION_NAMES:
      return "LIST_CODE_DEFINITION_NAMES";
    case ClineSayToolType.SEARCH_FILES:
      return "SEARCH_FILES";
    case ClineSayToolType.WEB_FETCH:
      return "WEB_FETCH";
    case ClineSayToolType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** Enum for browser actions */
export enum BrowserAction {
  LAUNCH = 0,
  CLICK = 1,
  TYPE = 2,
  SCROLL_DOWN = 3,
  SCROLL_UP = 4,
  CLOSE = 5,
  UNRECOGNIZED = -1,
}

export function browserActionFromJSON(object: any): BrowserAction {
  switch (object) {
    case 0:
    case "LAUNCH":
      return BrowserAction.LAUNCH;
    case 1:
    case "CLICK":
      return BrowserAction.CLICK;
    case 2:
    case "TYPE":
      return BrowserAction.TYPE;
    case 3:
    case "SCROLL_DOWN":
      return BrowserAction.SCROLL_DOWN;
    case 4:
    case "SCROLL_UP":
      return BrowserAction.SCROLL_UP;
    case 5:
    case "CLOSE":
      return BrowserAction.CLOSE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BrowserAction.UNRECOGNIZED;
  }
}

export function browserActionToJSON(object: BrowserAction): string {
  switch (object) {
    case BrowserAction.LAUNCH:
      return "LAUNCH";
    case BrowserAction.CLICK:
      return "CLICK";
    case BrowserAction.TYPE:
      return "TYPE";
    case BrowserAction.SCROLL_DOWN:
      return "SCROLL_DOWN";
    case BrowserAction.SCROLL_UP:
      return "SCROLL_UP";
    case BrowserAction.CLOSE:
      return "CLOSE";
    case BrowserAction.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** Enum for MCP server request types */
export enum McpServerRequestType {
  USE_MCP_TOOL = 0,
  ACCESS_MCP_RESOURCE = 1,
  UNRECOGNIZED = -1,
}

export function mcpServerRequestTypeFromJSON(object: any): McpServerRequestType {
  switch (object) {
    case 0:
    case "USE_MCP_TOOL":
      return McpServerRequestType.USE_MCP_TOOL;
    case 1:
    case "ACCESS_MCP_RESOURCE":
      return McpServerRequestType.ACCESS_MCP_RESOURCE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return McpServerRequestType.UNRECOGNIZED;
  }
}

export function mcpServerRequestTypeToJSON(object: McpServerRequestType): string {
  switch (object) {
    case McpServerRequestType.USE_MCP_TOOL:
      return "USE_MCP_TOOL";
    case McpServerRequestType.ACCESS_MCP_RESOURCE:
      return "ACCESS_MCP_RESOURCE";
    case McpServerRequestType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** Enum for API request cancel reasons */
export enum ClineApiReqCancelReason {
  STREAMING_FAILED = 0,
  USER_CANCELLED = 1,
  RETRIES_EXHAUSTED = 2,
  UNRECOGNIZED = -1,
}

export function clineApiReqCancelReasonFromJSON(object: any): ClineApiReqCancelReason {
  switch (object) {
    case 0:
    case "STREAMING_FAILED":
      return ClineApiReqCancelReason.STREAMING_FAILED;
    case 1:
    case "USER_CANCELLED":
      return ClineApiReqCancelReason.USER_CANCELLED;
    case 2:
    case "RETRIES_EXHAUSTED":
      return ClineApiReqCancelReason.RETRIES_EXHAUSTED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ClineApiReqCancelReason.UNRECOGNIZED;
  }
}

export function clineApiReqCancelReasonToJSON(object: ClineApiReqCancelReason): string {
  switch (object) {
    case ClineApiReqCancelReason.STREAMING_FAILED:
      return "STREAMING_FAILED";
    case ClineApiReqCancelReason.USER_CANCELLED:
      return "USER_CANCELLED";
    case ClineApiReqCancelReason.RETRIES_EXHAUSTED:
      return "RETRIES_EXHAUSTED";
    case ClineApiReqCancelReason.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** Define a new message type for webview provider info */
export interface WebviewProviderTypeRequest {
  metadata?: Metadata | undefined;
  providerType: WebviewProviderType;
}

/** Message for conversation history deleted range */
export interface ConversationHistoryDeletedRange {
  startIndex: number;
  endIndex: number;
}

/** Message for ClineSayTool */
export interface ClineSayTool {
  tool: ClineSayToolType;
  path: string;
  diff: string;
  content: string;
  regex: string;
  filePattern: string;
  operationIsLocatedInWorkspace: boolean;
}

/** Message for ClineSayBrowserAction */
export interface ClineSayBrowserAction {
  action: BrowserAction;
  coordinate: string;
  text: string;
}

/** Message for BrowserActionResult */
export interface BrowserActionResult {
  screenshot: string;
  logs: string;
  currentUrl: string;
  currentMousePosition: string;
}

/** Message for ClineAskUseMcpServer */
export interface ClineAskUseMcpServer {
  serverName: string;
  type: McpServerRequestType;
  toolName: string;
  arguments: string;
  uri: string;
}

/** Message for ClinePlanModeResponse */
export interface ClinePlanModeResponse {
  response: string;
  options: string[];
  selected: string;
}

/** Message for ClineAskQuestion */
export interface ClineAskQuestion {
  question: string;
  options: string[];
  selected: string;
}

/** Message for ClineAskNewTask */
export interface ClineAskNewTask {
  context: string;
}

/** Message for API request retry status */
export interface ApiReqRetryStatus {
  attempt: number;
  maxAttempts: number;
  delaySec: number;
  errorSnippet: string;
}

/** Message for ClineApiReqInfo */
export interface ClineApiReqInfo {
  request: string;
  tokensIn: number;
  tokensOut: number;
  cacheWrites: number;
  cacheReads: number;
  cost: number;
  cancelReason: ClineApiReqCancelReason;
  streamingFailedMessage: string;
  retryStatus?: ApiReqRetryStatus | undefined;
}

/** Main ClineMessage type */
export interface ClineMessage {
  ts: number;
  type: ClineMessageType;
  ask: ClineAsk;
  say: ClineSay;
  text: string;
  reasoning: string;
  images: string[];
  files: string[];
  partial: boolean;
  lastCheckpointHash: string;
  isCheckpointCheckedOut: boolean;
  isOperationOutsideWorkspace: boolean;
  conversationHistoryIndex: number;
  conversationHistoryDeletedRange?:
    | ConversationHistoryDeletedRange
    | undefined;
  /** Additional fields for specific ask/say types */
  sayTool?: ClineSayTool | undefined;
  sayBrowserAction?: ClineSayBrowserAction | undefined;
  browserActionResult?: BrowserActionResult | undefined;
  askUseMcpServer?: ClineAskUseMcpServer | undefined;
  planModeResponse?: ClinePlanModeResponse | undefined;
  askQuestion?: ClineAskQuestion | undefined;
  askNewTask?: ClineAskNewTask | undefined;
  apiReqInfo?: ClineApiReqInfo | undefined;
}

function createBaseWebviewProviderTypeRequest(): WebviewProviderTypeRequest {
  return { metadata: undefined, providerType: 0 };
}

export const WebviewProviderTypeRequest: MessageFns<WebviewProviderTypeRequest> = {
  encode(message: WebviewProviderTypeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join();
    }
    if (message.providerType !== 0) {
      writer.uint32(16).int32(message.providerType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WebviewProviderTypeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWebviewProviderTypeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.metadata = Metadata.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.providerType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WebviewProviderTypeRequest {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      providerType: isSet(object.providerType) ? webviewProviderTypeFromJSON(object.providerType) : 0,
    };
  },

  toJSON(message: WebviewProviderTypeRequest): unknown {
    const obj: any = {};
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata);
    }
    if (message.providerType !== 0) {
      obj.providerType = webviewProviderTypeToJSON(message.providerType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WebviewProviderTypeRequest>, I>>(base?: I): WebviewProviderTypeRequest {
    return WebviewProviderTypeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WebviewProviderTypeRequest>, I>>(object: I): WebviewProviderTypeRequest {
    const message = createBaseWebviewProviderTypeRequest();
    message.metadata = (object.metadata !== undefined && object.metadata !== null)
      ? Metadata.fromPartial(object.metadata)
      : undefined;
    message.providerType = object.providerType ?? 0;
    return message;
  },
};

function createBaseConversationHistoryDeletedRange(): ConversationHistoryDeletedRange {
  return { startIndex: 0, endIndex: 0 };
}

export const ConversationHistoryDeletedRange: MessageFns<ConversationHistoryDeletedRange> = {
  encode(message: ConversationHistoryDeletedRange, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.startIndex !== 0) {
      writer.uint32(8).int32(message.startIndex);
    }
    if (message.endIndex !== 0) {
      writer.uint32(16).int32(message.endIndex);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConversationHistoryDeletedRange {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConversationHistoryDeletedRange();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.startIndex = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.endIndex = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ConversationHistoryDeletedRange {
    return {
      startIndex: isSet(object.startIndex) ? globalThis.Number(object.startIndex) : 0,
      endIndex: isSet(object.endIndex) ? globalThis.Number(object.endIndex) : 0,
    };
  },

  toJSON(message: ConversationHistoryDeletedRange): unknown {
    const obj: any = {};
    if (message.startIndex !== 0) {
      obj.startIndex = Math.round(message.startIndex);
    }
    if (message.endIndex !== 0) {
      obj.endIndex = Math.round(message.endIndex);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConversationHistoryDeletedRange>, I>>(base?: I): ConversationHistoryDeletedRange {
    return ConversationHistoryDeletedRange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConversationHistoryDeletedRange>, I>>(
    object: I,
  ): ConversationHistoryDeletedRange {
    const message = createBaseConversationHistoryDeletedRange();
    message.startIndex = object.startIndex ?? 0;
    message.endIndex = object.endIndex ?? 0;
    return message;
  },
};

function createBaseClineSayTool(): ClineSayTool {
  return { tool: 0, path: "", diff: "", content: "", regex: "", filePattern: "", operationIsLocatedInWorkspace: false };
}

export const ClineSayTool: MessageFns<ClineSayTool> = {
  encode(message: ClineSayTool, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tool !== 0) {
      writer.uint32(8).int32(message.tool);
    }
    if (message.path !== "") {
      writer.uint32(18).string(message.path);
    }
    if (message.diff !== "") {
      writer.uint32(26).string(message.diff);
    }
    if (message.content !== "") {
      writer.uint32(34).string(message.content);
    }
    if (message.regex !== "") {
      writer.uint32(42).string(message.regex);
    }
    if (message.filePattern !== "") {
      writer.uint32(50).string(message.filePattern);
    }
    if (message.operationIsLocatedInWorkspace !== false) {
      writer.uint32(56).bool(message.operationIsLocatedInWorkspace);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClineSayTool {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClineSayTool();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.tool = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.path = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.diff = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.regex = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.filePattern = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.operationIsLocatedInWorkspace = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClineSayTool {
    return {
      tool: isSet(object.tool) ? clineSayToolTypeFromJSON(object.tool) : 0,
      path: isSet(object.path) ? globalThis.String(object.path) : "",
      diff: isSet(object.diff) ? globalThis.String(object.diff) : "",
      content: isSet(object.content) ? globalThis.String(object.content) : "",
      regex: isSet(object.regex) ? globalThis.String(object.regex) : "",
      filePattern: isSet(object.filePattern) ? globalThis.String(object.filePattern) : "",
      operationIsLocatedInWorkspace: isSet(object.operationIsLocatedInWorkspace)
        ? globalThis.Boolean(object.operationIsLocatedInWorkspace)
        : false,
    };
  },

  toJSON(message: ClineSayTool): unknown {
    const obj: any = {};
    if (message.tool !== 0) {
      obj.tool = clineSayToolTypeToJSON(message.tool);
    }
    if (message.path !== "") {
      obj.path = message.path;
    }
    if (message.diff !== "") {
      obj.diff = message.diff;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    if (message.regex !== "") {
      obj.regex = message.regex;
    }
    if (message.filePattern !== "") {
      obj.filePattern = message.filePattern;
    }
    if (message.operationIsLocatedInWorkspace !== false) {
      obj.operationIsLocatedInWorkspace = message.operationIsLocatedInWorkspace;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClineSayTool>, I>>(base?: I): ClineSayTool {
    return ClineSayTool.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClineSayTool>, I>>(object: I): ClineSayTool {
    const message = createBaseClineSayTool();
    message.tool = object.tool ?? 0;
    message.path = object.path ?? "";
    message.diff = object.diff ?? "";
    message.content = object.content ?? "";
    message.regex = object.regex ?? "";
    message.filePattern = object.filePattern ?? "";
    message.operationIsLocatedInWorkspace = object.operationIsLocatedInWorkspace ?? false;
    return message;
  },
};

function createBaseClineSayBrowserAction(): ClineSayBrowserAction {
  return { action: 0, coordinate: "", text: "" };
}

export const ClineSayBrowserAction: MessageFns<ClineSayBrowserAction> = {
  encode(message: ClineSayBrowserAction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.action !== 0) {
      writer.uint32(8).int32(message.action);
    }
    if (message.coordinate !== "") {
      writer.uint32(18).string(message.coordinate);
    }
    if (message.text !== "") {
      writer.uint32(26).string(message.text);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClineSayBrowserAction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClineSayBrowserAction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.action = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.coordinate = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.text = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClineSayBrowserAction {
    return {
      action: isSet(object.action) ? browserActionFromJSON(object.action) : 0,
      coordinate: isSet(object.coordinate) ? globalThis.String(object.coordinate) : "",
      text: isSet(object.text) ? globalThis.String(object.text) : "",
    };
  },

  toJSON(message: ClineSayBrowserAction): unknown {
    const obj: any = {};
    if (message.action !== 0) {
      obj.action = browserActionToJSON(message.action);
    }
    if (message.coordinate !== "") {
      obj.coordinate = message.coordinate;
    }
    if (message.text !== "") {
      obj.text = message.text;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClineSayBrowserAction>, I>>(base?: I): ClineSayBrowserAction {
    return ClineSayBrowserAction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClineSayBrowserAction>, I>>(object: I): ClineSayBrowserAction {
    const message = createBaseClineSayBrowserAction();
    message.action = object.action ?? 0;
    message.coordinate = object.coordinate ?? "";
    message.text = object.text ?? "";
    return message;
  },
};

function createBaseBrowserActionResult(): BrowserActionResult {
  return { screenshot: "", logs: "", currentUrl: "", currentMousePosition: "" };
}

export const BrowserActionResult: MessageFns<BrowserActionResult> = {
  encode(message: BrowserActionResult, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.screenshot !== "") {
      writer.uint32(10).string(message.screenshot);
    }
    if (message.logs !== "") {
      writer.uint32(18).string(message.logs);
    }
    if (message.currentUrl !== "") {
      writer.uint32(26).string(message.currentUrl);
    }
    if (message.currentMousePosition !== "") {
      writer.uint32(34).string(message.currentMousePosition);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BrowserActionResult {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBrowserActionResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.screenshot = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.logs = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.currentUrl = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.currentMousePosition = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BrowserActionResult {
    return {
      screenshot: isSet(object.screenshot) ? globalThis.String(object.screenshot) : "",
      logs: isSet(object.logs) ? globalThis.String(object.logs) : "",
      currentUrl: isSet(object.currentUrl) ? globalThis.String(object.currentUrl) : "",
      currentMousePosition: isSet(object.currentMousePosition) ? globalThis.String(object.currentMousePosition) : "",
    };
  },

  toJSON(message: BrowserActionResult): unknown {
    const obj: any = {};
    if (message.screenshot !== "") {
      obj.screenshot = message.screenshot;
    }
    if (message.logs !== "") {
      obj.logs = message.logs;
    }
    if (message.currentUrl !== "") {
      obj.currentUrl = message.currentUrl;
    }
    if (message.currentMousePosition !== "") {
      obj.currentMousePosition = message.currentMousePosition;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BrowserActionResult>, I>>(base?: I): BrowserActionResult {
    return BrowserActionResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BrowserActionResult>, I>>(object: I): BrowserActionResult {
    const message = createBaseBrowserActionResult();
    message.screenshot = object.screenshot ?? "";
    message.logs = object.logs ?? "";
    message.currentUrl = object.currentUrl ?? "";
    message.currentMousePosition = object.currentMousePosition ?? "";
    return message;
  },
};

function createBaseClineAskUseMcpServer(): ClineAskUseMcpServer {
  return { serverName: "", type: 0, toolName: "", arguments: "", uri: "" };
}

export const ClineAskUseMcpServer: MessageFns<ClineAskUseMcpServer> = {
  encode(message: ClineAskUseMcpServer, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.serverName !== "") {
      writer.uint32(10).string(message.serverName);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    if (message.toolName !== "") {
      writer.uint32(26).string(message.toolName);
    }
    if (message.arguments !== "") {
      writer.uint32(34).string(message.arguments);
    }
    if (message.uri !== "") {
      writer.uint32(42).string(message.uri);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClineAskUseMcpServer {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClineAskUseMcpServer();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.serverName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.toolName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.arguments = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.uri = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClineAskUseMcpServer {
    return {
      serverName: isSet(object.serverName) ? globalThis.String(object.serverName) : "",
      type: isSet(object.type) ? mcpServerRequestTypeFromJSON(object.type) : 0,
      toolName: isSet(object.toolName) ? globalThis.String(object.toolName) : "",
      arguments: isSet(object.arguments) ? globalThis.String(object.arguments) : "",
      uri: isSet(object.uri) ? globalThis.String(object.uri) : "",
    };
  },

  toJSON(message: ClineAskUseMcpServer): unknown {
    const obj: any = {};
    if (message.serverName !== "") {
      obj.serverName = message.serverName;
    }
    if (message.type !== 0) {
      obj.type = mcpServerRequestTypeToJSON(message.type);
    }
    if (message.toolName !== "") {
      obj.toolName = message.toolName;
    }
    if (message.arguments !== "") {
      obj.arguments = message.arguments;
    }
    if (message.uri !== "") {
      obj.uri = message.uri;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClineAskUseMcpServer>, I>>(base?: I): ClineAskUseMcpServer {
    return ClineAskUseMcpServer.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClineAskUseMcpServer>, I>>(object: I): ClineAskUseMcpServer {
    const message = createBaseClineAskUseMcpServer();
    message.serverName = object.serverName ?? "";
    message.type = object.type ?? 0;
    message.toolName = object.toolName ?? "";
    message.arguments = object.arguments ?? "";
    message.uri = object.uri ?? "";
    return message;
  },
};

function createBaseClinePlanModeResponse(): ClinePlanModeResponse {
  return { response: "", options: [], selected: "" };
}

export const ClinePlanModeResponse: MessageFns<ClinePlanModeResponse> = {
  encode(message: ClinePlanModeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.response !== "") {
      writer.uint32(10).string(message.response);
    }
    for (const v of message.options) {
      writer.uint32(18).string(v!);
    }
    if (message.selected !== "") {
      writer.uint32(26).string(message.selected);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClinePlanModeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClinePlanModeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.response = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.options.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.selected = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClinePlanModeResponse {
    return {
      response: isSet(object.response) ? globalThis.String(object.response) : "",
      options: globalThis.Array.isArray(object?.options) ? object.options.map((e: any) => globalThis.String(e)) : [],
      selected: isSet(object.selected) ? globalThis.String(object.selected) : "",
    };
  },

  toJSON(message: ClinePlanModeResponse): unknown {
    const obj: any = {};
    if (message.response !== "") {
      obj.response = message.response;
    }
    if (message.options?.length) {
      obj.options = message.options;
    }
    if (message.selected !== "") {
      obj.selected = message.selected;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClinePlanModeResponse>, I>>(base?: I): ClinePlanModeResponse {
    return ClinePlanModeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClinePlanModeResponse>, I>>(object: I): ClinePlanModeResponse {
    const message = createBaseClinePlanModeResponse();
    message.response = object.response ?? "";
    message.options = object.options?.map((e) => e) || [];
    message.selected = object.selected ?? "";
    return message;
  },
};

function createBaseClineAskQuestion(): ClineAskQuestion {
  return { question: "", options: [], selected: "" };
}

export const ClineAskQuestion: MessageFns<ClineAskQuestion> = {
  encode(message: ClineAskQuestion, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.question !== "") {
      writer.uint32(10).string(message.question);
    }
    for (const v of message.options) {
      writer.uint32(18).string(v!);
    }
    if (message.selected !== "") {
      writer.uint32(26).string(message.selected);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClineAskQuestion {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClineAskQuestion();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.question = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.options.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.selected = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClineAskQuestion {
    return {
      question: isSet(object.question) ? globalThis.String(object.question) : "",
      options: globalThis.Array.isArray(object?.options) ? object.options.map((e: any) => globalThis.String(e)) : [],
      selected: isSet(object.selected) ? globalThis.String(object.selected) : "",
    };
  },

  toJSON(message: ClineAskQuestion): unknown {
    const obj: any = {};
    if (message.question !== "") {
      obj.question = message.question;
    }
    if (message.options?.length) {
      obj.options = message.options;
    }
    if (message.selected !== "") {
      obj.selected = message.selected;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClineAskQuestion>, I>>(base?: I): ClineAskQuestion {
    return ClineAskQuestion.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClineAskQuestion>, I>>(object: I): ClineAskQuestion {
    const message = createBaseClineAskQuestion();
    message.question = object.question ?? "";
    message.options = object.options?.map((e) => e) || [];
    message.selected = object.selected ?? "";
    return message;
  },
};

function createBaseClineAskNewTask(): ClineAskNewTask {
  return { context: "" };
}

export const ClineAskNewTask: MessageFns<ClineAskNewTask> = {
  encode(message: ClineAskNewTask, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.context !== "") {
      writer.uint32(10).string(message.context);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClineAskNewTask {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClineAskNewTask();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.context = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClineAskNewTask {
    return { context: isSet(object.context) ? globalThis.String(object.context) : "" };
  },

  toJSON(message: ClineAskNewTask): unknown {
    const obj: any = {};
    if (message.context !== "") {
      obj.context = message.context;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClineAskNewTask>, I>>(base?: I): ClineAskNewTask {
    return ClineAskNewTask.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClineAskNewTask>, I>>(object: I): ClineAskNewTask {
    const message = createBaseClineAskNewTask();
    message.context = object.context ?? "";
    return message;
  },
};

function createBaseApiReqRetryStatus(): ApiReqRetryStatus {
  return { attempt: 0, maxAttempts: 0, delaySec: 0, errorSnippet: "" };
}

export const ApiReqRetryStatus: MessageFns<ApiReqRetryStatus> = {
  encode(message: ApiReqRetryStatus, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.attempt !== 0) {
      writer.uint32(8).int32(message.attempt);
    }
    if (message.maxAttempts !== 0) {
      writer.uint32(16).int32(message.maxAttempts);
    }
    if (message.delaySec !== 0) {
      writer.uint32(24).int32(message.delaySec);
    }
    if (message.errorSnippet !== "") {
      writer.uint32(34).string(message.errorSnippet);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ApiReqRetryStatus {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseApiReqRetryStatus();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.attempt = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.maxAttempts = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.delaySec = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.errorSnippet = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ApiReqRetryStatus {
    return {
      attempt: isSet(object.attempt) ? globalThis.Number(object.attempt) : 0,
      maxAttempts: isSet(object.maxAttempts) ? globalThis.Number(object.maxAttempts) : 0,
      delaySec: isSet(object.delaySec) ? globalThis.Number(object.delaySec) : 0,
      errorSnippet: isSet(object.errorSnippet) ? globalThis.String(object.errorSnippet) : "",
    };
  },

  toJSON(message: ApiReqRetryStatus): unknown {
    const obj: any = {};
    if (message.attempt !== 0) {
      obj.attempt = Math.round(message.attempt);
    }
    if (message.maxAttempts !== 0) {
      obj.maxAttempts = Math.round(message.maxAttempts);
    }
    if (message.delaySec !== 0) {
      obj.delaySec = Math.round(message.delaySec);
    }
    if (message.errorSnippet !== "") {
      obj.errorSnippet = message.errorSnippet;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ApiReqRetryStatus>, I>>(base?: I): ApiReqRetryStatus {
    return ApiReqRetryStatus.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApiReqRetryStatus>, I>>(object: I): ApiReqRetryStatus {
    const message = createBaseApiReqRetryStatus();
    message.attempt = object.attempt ?? 0;
    message.maxAttempts = object.maxAttempts ?? 0;
    message.delaySec = object.delaySec ?? 0;
    message.errorSnippet = object.errorSnippet ?? "";
    return message;
  },
};

function createBaseClineApiReqInfo(): ClineApiReqInfo {
  return {
    request: "",
    tokensIn: 0,
    tokensOut: 0,
    cacheWrites: 0,
    cacheReads: 0,
    cost: 0,
    cancelReason: 0,
    streamingFailedMessage: "",
    retryStatus: undefined,
  };
}

export const ClineApiReqInfo: MessageFns<ClineApiReqInfo> = {
  encode(message: ClineApiReqInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.request !== "") {
      writer.uint32(10).string(message.request);
    }
    if (message.tokensIn !== 0) {
      writer.uint32(16).int32(message.tokensIn);
    }
    if (message.tokensOut !== 0) {
      writer.uint32(24).int32(message.tokensOut);
    }
    if (message.cacheWrites !== 0) {
      writer.uint32(32).int32(message.cacheWrites);
    }
    if (message.cacheReads !== 0) {
      writer.uint32(40).int32(message.cacheReads);
    }
    if (message.cost !== 0) {
      writer.uint32(49).double(message.cost);
    }
    if (message.cancelReason !== 0) {
      writer.uint32(56).int32(message.cancelReason);
    }
    if (message.streamingFailedMessage !== "") {
      writer.uint32(66).string(message.streamingFailedMessage);
    }
    if (message.retryStatus !== undefined) {
      ApiReqRetryStatus.encode(message.retryStatus, writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClineApiReqInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClineApiReqInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.request = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.tokensIn = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.tokensOut = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.cacheWrites = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.cacheReads = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.cost = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.cancelReason = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.streamingFailedMessage = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.retryStatus = ApiReqRetryStatus.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClineApiReqInfo {
    return {
      request: isSet(object.request) ? globalThis.String(object.request) : "",
      tokensIn: isSet(object.tokensIn) ? globalThis.Number(object.tokensIn) : 0,
      tokensOut: isSet(object.tokensOut) ? globalThis.Number(object.tokensOut) : 0,
      cacheWrites: isSet(object.cacheWrites) ? globalThis.Number(object.cacheWrites) : 0,
      cacheReads: isSet(object.cacheReads) ? globalThis.Number(object.cacheReads) : 0,
      cost: isSet(object.cost) ? globalThis.Number(object.cost) : 0,
      cancelReason: isSet(object.cancelReason) ? clineApiReqCancelReasonFromJSON(object.cancelReason) : 0,
      streamingFailedMessage: isSet(object.streamingFailedMessage)
        ? globalThis.String(object.streamingFailedMessage)
        : "",
      retryStatus: isSet(object.retryStatus) ? ApiReqRetryStatus.fromJSON(object.retryStatus) : undefined,
    };
  },

  toJSON(message: ClineApiReqInfo): unknown {
    const obj: any = {};
    if (message.request !== "") {
      obj.request = message.request;
    }
    if (message.tokensIn !== 0) {
      obj.tokensIn = Math.round(message.tokensIn);
    }
    if (message.tokensOut !== 0) {
      obj.tokensOut = Math.round(message.tokensOut);
    }
    if (message.cacheWrites !== 0) {
      obj.cacheWrites = Math.round(message.cacheWrites);
    }
    if (message.cacheReads !== 0) {
      obj.cacheReads = Math.round(message.cacheReads);
    }
    if (message.cost !== 0) {
      obj.cost = message.cost;
    }
    if (message.cancelReason !== 0) {
      obj.cancelReason = clineApiReqCancelReasonToJSON(message.cancelReason);
    }
    if (message.streamingFailedMessage !== "") {
      obj.streamingFailedMessage = message.streamingFailedMessage;
    }
    if (message.retryStatus !== undefined) {
      obj.retryStatus = ApiReqRetryStatus.toJSON(message.retryStatus);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClineApiReqInfo>, I>>(base?: I): ClineApiReqInfo {
    return ClineApiReqInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClineApiReqInfo>, I>>(object: I): ClineApiReqInfo {
    const message = createBaseClineApiReqInfo();
    message.request = object.request ?? "";
    message.tokensIn = object.tokensIn ?? 0;
    message.tokensOut = object.tokensOut ?? 0;
    message.cacheWrites = object.cacheWrites ?? 0;
    message.cacheReads = object.cacheReads ?? 0;
    message.cost = object.cost ?? 0;
    message.cancelReason = object.cancelReason ?? 0;
    message.streamingFailedMessage = object.streamingFailedMessage ?? "";
    message.retryStatus = (object.retryStatus !== undefined && object.retryStatus !== null)
      ? ApiReqRetryStatus.fromPartial(object.retryStatus)
      : undefined;
    return message;
  },
};

function createBaseClineMessage(): ClineMessage {
  return {
    ts: 0,
    type: 0,
    ask: 0,
    say: 0,
    text: "",
    reasoning: "",
    images: [],
    files: [],
    partial: false,
    lastCheckpointHash: "",
    isCheckpointCheckedOut: false,
    isOperationOutsideWorkspace: false,
    conversationHistoryIndex: 0,
    conversationHistoryDeletedRange: undefined,
    sayTool: undefined,
    sayBrowserAction: undefined,
    browserActionResult: undefined,
    askUseMcpServer: undefined,
    planModeResponse: undefined,
    askQuestion: undefined,
    askNewTask: undefined,
    apiReqInfo: undefined,
  };
}

export const ClineMessage: MessageFns<ClineMessage> = {
  encode(message: ClineMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ts !== 0) {
      writer.uint32(8).int64(message.ts);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    if (message.ask !== 0) {
      writer.uint32(24).int32(message.ask);
    }
    if (message.say !== 0) {
      writer.uint32(32).int32(message.say);
    }
    if (message.text !== "") {
      writer.uint32(42).string(message.text);
    }
    if (message.reasoning !== "") {
      writer.uint32(50).string(message.reasoning);
    }
    for (const v of message.images) {
      writer.uint32(58).string(v!);
    }
    for (const v of message.files) {
      writer.uint32(66).string(v!);
    }
    if (message.partial !== false) {
      writer.uint32(72).bool(message.partial);
    }
    if (message.lastCheckpointHash !== "") {
      writer.uint32(82).string(message.lastCheckpointHash);
    }
    if (message.isCheckpointCheckedOut !== false) {
      writer.uint32(88).bool(message.isCheckpointCheckedOut);
    }
    if (message.isOperationOutsideWorkspace !== false) {
      writer.uint32(96).bool(message.isOperationOutsideWorkspace);
    }
    if (message.conversationHistoryIndex !== 0) {
      writer.uint32(104).int32(message.conversationHistoryIndex);
    }
    if (message.conversationHistoryDeletedRange !== undefined) {
      ConversationHistoryDeletedRange.encode(message.conversationHistoryDeletedRange, writer.uint32(114).fork()).join();
    }
    if (message.sayTool !== undefined) {
      ClineSayTool.encode(message.sayTool, writer.uint32(122).fork()).join();
    }
    if (message.sayBrowserAction !== undefined) {
      ClineSayBrowserAction.encode(message.sayBrowserAction, writer.uint32(130).fork()).join();
    }
    if (message.browserActionResult !== undefined) {
      BrowserActionResult.encode(message.browserActionResult, writer.uint32(138).fork()).join();
    }
    if (message.askUseMcpServer !== undefined) {
      ClineAskUseMcpServer.encode(message.askUseMcpServer, writer.uint32(146).fork()).join();
    }
    if (message.planModeResponse !== undefined) {
      ClinePlanModeResponse.encode(message.planModeResponse, writer.uint32(154).fork()).join();
    }
    if (message.askQuestion !== undefined) {
      ClineAskQuestion.encode(message.askQuestion, writer.uint32(162).fork()).join();
    }
    if (message.askNewTask !== undefined) {
      ClineAskNewTask.encode(message.askNewTask, writer.uint32(170).fork()).join();
    }
    if (message.apiReqInfo !== undefined) {
      ClineApiReqInfo.encode(message.apiReqInfo, writer.uint32(178).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClineMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClineMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.ts = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.ask = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.say = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.text = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.reasoning = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.images.push(reader.string());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.files.push(reader.string());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.partial = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.lastCheckpointHash = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.isCheckpointCheckedOut = reader.bool();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.isOperationOutsideWorkspace = reader.bool();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.conversationHistoryIndex = reader.int32();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.conversationHistoryDeletedRange = ConversationHistoryDeletedRange.decode(reader, reader.uint32());
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.sayTool = ClineSayTool.decode(reader, reader.uint32());
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.sayBrowserAction = ClineSayBrowserAction.decode(reader, reader.uint32());
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.browserActionResult = BrowserActionResult.decode(reader, reader.uint32());
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.askUseMcpServer = ClineAskUseMcpServer.decode(reader, reader.uint32());
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.planModeResponse = ClinePlanModeResponse.decode(reader, reader.uint32());
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.askQuestion = ClineAskQuestion.decode(reader, reader.uint32());
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.askNewTask = ClineAskNewTask.decode(reader, reader.uint32());
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.apiReqInfo = ClineApiReqInfo.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClineMessage {
    return {
      ts: isSet(object.ts) ? globalThis.Number(object.ts) : 0,
      type: isSet(object.type) ? clineMessageTypeFromJSON(object.type) : 0,
      ask: isSet(object.ask) ? clineAskFromJSON(object.ask) : 0,
      say: isSet(object.say) ? clineSayFromJSON(object.say) : 0,
      text: isSet(object.text) ? globalThis.String(object.text) : "",
      reasoning: isSet(object.reasoning) ? globalThis.String(object.reasoning) : "",
      images: globalThis.Array.isArray(object?.images) ? object.images.map((e: any) => globalThis.String(e)) : [],
      files: globalThis.Array.isArray(object?.files) ? object.files.map((e: any) => globalThis.String(e)) : [],
      partial: isSet(object.partial) ? globalThis.Boolean(object.partial) : false,
      lastCheckpointHash: isSet(object.lastCheckpointHash) ? globalThis.String(object.lastCheckpointHash) : "",
      isCheckpointCheckedOut: isSet(object.isCheckpointCheckedOut)
        ? globalThis.Boolean(object.isCheckpointCheckedOut)
        : false,
      isOperationOutsideWorkspace: isSet(object.isOperationOutsideWorkspace)
        ? globalThis.Boolean(object.isOperationOutsideWorkspace)
        : false,
      conversationHistoryIndex: isSet(object.conversationHistoryIndex)
        ? globalThis.Number(object.conversationHistoryIndex)
        : 0,
      conversationHistoryDeletedRange: isSet(object.conversationHistoryDeletedRange)
        ? ConversationHistoryDeletedRange.fromJSON(object.conversationHistoryDeletedRange)
        : undefined,
      sayTool: isSet(object.sayTool) ? ClineSayTool.fromJSON(object.sayTool) : undefined,
      sayBrowserAction: isSet(object.sayBrowserAction)
        ? ClineSayBrowserAction.fromJSON(object.sayBrowserAction)
        : undefined,
      browserActionResult: isSet(object.browserActionResult)
        ? BrowserActionResult.fromJSON(object.browserActionResult)
        : undefined,
      askUseMcpServer: isSet(object.askUseMcpServer)
        ? ClineAskUseMcpServer.fromJSON(object.askUseMcpServer)
        : undefined,
      planModeResponse: isSet(object.planModeResponse)
        ? ClinePlanModeResponse.fromJSON(object.planModeResponse)
        : undefined,
      askQuestion: isSet(object.askQuestion) ? ClineAskQuestion.fromJSON(object.askQuestion) : undefined,
      askNewTask: isSet(object.askNewTask) ? ClineAskNewTask.fromJSON(object.askNewTask) : undefined,
      apiReqInfo: isSet(object.apiReqInfo) ? ClineApiReqInfo.fromJSON(object.apiReqInfo) : undefined,
    };
  },

  toJSON(message: ClineMessage): unknown {
    const obj: any = {};
    if (message.ts !== 0) {
      obj.ts = Math.round(message.ts);
    }
    if (message.type !== 0) {
      obj.type = clineMessageTypeToJSON(message.type);
    }
    if (message.ask !== 0) {
      obj.ask = clineAskToJSON(message.ask);
    }
    if (message.say !== 0) {
      obj.say = clineSayToJSON(message.say);
    }
    if (message.text !== "") {
      obj.text = message.text;
    }
    if (message.reasoning !== "") {
      obj.reasoning = message.reasoning;
    }
    if (message.images?.length) {
      obj.images = message.images;
    }
    if (message.files?.length) {
      obj.files = message.files;
    }
    if (message.partial !== false) {
      obj.partial = message.partial;
    }
    if (message.lastCheckpointHash !== "") {
      obj.lastCheckpointHash = message.lastCheckpointHash;
    }
    if (message.isCheckpointCheckedOut !== false) {
      obj.isCheckpointCheckedOut = message.isCheckpointCheckedOut;
    }
    if (message.isOperationOutsideWorkspace !== false) {
      obj.isOperationOutsideWorkspace = message.isOperationOutsideWorkspace;
    }
    if (message.conversationHistoryIndex !== 0) {
      obj.conversationHistoryIndex = Math.round(message.conversationHistoryIndex);
    }
    if (message.conversationHistoryDeletedRange !== undefined) {
      obj.conversationHistoryDeletedRange = ConversationHistoryDeletedRange.toJSON(
        message.conversationHistoryDeletedRange,
      );
    }
    if (message.sayTool !== undefined) {
      obj.sayTool = ClineSayTool.toJSON(message.sayTool);
    }
    if (message.sayBrowserAction !== undefined) {
      obj.sayBrowserAction = ClineSayBrowserAction.toJSON(message.sayBrowserAction);
    }
    if (message.browserActionResult !== undefined) {
      obj.browserActionResult = BrowserActionResult.toJSON(message.browserActionResult);
    }
    if (message.askUseMcpServer !== undefined) {
      obj.askUseMcpServer = ClineAskUseMcpServer.toJSON(message.askUseMcpServer);
    }
    if (message.planModeResponse !== undefined) {
      obj.planModeResponse = ClinePlanModeResponse.toJSON(message.planModeResponse);
    }
    if (message.askQuestion !== undefined) {
      obj.askQuestion = ClineAskQuestion.toJSON(message.askQuestion);
    }
    if (message.askNewTask !== undefined) {
      obj.askNewTask = ClineAskNewTask.toJSON(message.askNewTask);
    }
    if (message.apiReqInfo !== undefined) {
      obj.apiReqInfo = ClineApiReqInfo.toJSON(message.apiReqInfo);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClineMessage>, I>>(base?: I): ClineMessage {
    return ClineMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClineMessage>, I>>(object: I): ClineMessage {
    const message = createBaseClineMessage();
    message.ts = object.ts ?? 0;
    message.type = object.type ?? 0;
    message.ask = object.ask ?? 0;
    message.say = object.say ?? 0;
    message.text = object.text ?? "";
    message.reasoning = object.reasoning ?? "";
    message.images = object.images?.map((e) => e) || [];
    message.files = object.files?.map((e) => e) || [];
    message.partial = object.partial ?? false;
    message.lastCheckpointHash = object.lastCheckpointHash ?? "";
    message.isCheckpointCheckedOut = object.isCheckpointCheckedOut ?? false;
    message.isOperationOutsideWorkspace = object.isOperationOutsideWorkspace ?? false;
    message.conversationHistoryIndex = object.conversationHistoryIndex ?? 0;
    message.conversationHistoryDeletedRange =
      (object.conversationHistoryDeletedRange !== undefined && object.conversationHistoryDeletedRange !== null)
        ? ConversationHistoryDeletedRange.fromPartial(object.conversationHistoryDeletedRange)
        : undefined;
    message.sayTool = (object.sayTool !== undefined && object.sayTool !== null)
      ? ClineSayTool.fromPartial(object.sayTool)
      : undefined;
    message.sayBrowserAction = (object.sayBrowserAction !== undefined && object.sayBrowserAction !== null)
      ? ClineSayBrowserAction.fromPartial(object.sayBrowserAction)
      : undefined;
    message.browserActionResult = (object.browserActionResult !== undefined && object.browserActionResult !== null)
      ? BrowserActionResult.fromPartial(object.browserActionResult)
      : undefined;
    message.askUseMcpServer = (object.askUseMcpServer !== undefined && object.askUseMcpServer !== null)
      ? ClineAskUseMcpServer.fromPartial(object.askUseMcpServer)
      : undefined;
    message.planModeResponse = (object.planModeResponse !== undefined && object.planModeResponse !== null)
      ? ClinePlanModeResponse.fromPartial(object.planModeResponse)
      : undefined;
    message.askQuestion = (object.askQuestion !== undefined && object.askQuestion !== null)
      ? ClineAskQuestion.fromPartial(object.askQuestion)
      : undefined;
    message.askNewTask = (object.askNewTask !== undefined && object.askNewTask !== null)
      ? ClineAskNewTask.fromPartial(object.askNewTask)
      : undefined;
    message.apiReqInfo = (object.apiReqInfo !== undefined && object.apiReqInfo !== null)
      ? ClineApiReqInfo.fromPartial(object.apiReqInfo)
      : undefined;
    return message;
  },
};

/** UiService provides methods for managing UI interactions */
export type UiServiceDefinition = typeof UiServiceDefinition;
export const UiServiceDefinition = {
  name: "UiService",
  fullName: "cline.UiService",
  methods: {
    /** Scrolls to a specific settings section in the settings view */
    scrollToSettings: {
      name: "scrollToSettings",
      requestType: StringRequest,
      requestStream: false,
      responseType: KeyValuePair,
      responseStream: false,
      options: {},
    },
    /** Marks the current announcement as shown and returns whether an announcement should still be shown */
    onDidShowAnnouncement: {
      name: "onDidShowAnnouncement",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: Boolean,
      responseStream: false,
      options: {},
    },
    /** Subscribe to addToInput events (when user adds content via context menu) */
    subscribeToAddToInput: {
      name: "subscribeToAddToInput",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: String,
      responseStream: true,
      options: {},
    },
    /** Subscribe to MCP button clicked events */
    subscribeToMcpButtonClicked: {
      name: "subscribeToMcpButtonClicked",
      requestType: WebviewProviderTypeRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: true,
      options: {},
    },
    /** Subscribe to history button click events */
    subscribeToHistoryButtonClicked: {
      name: "subscribeToHistoryButtonClicked",
      requestType: WebviewProviderTypeRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: true,
      options: {},
    },
    /** Subscribe to chat button clicked events (when the chat button is clicked in VSCode) */
    subscribeToChatButtonClicked: {
      name: "subscribeToChatButtonClicked",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: true,
      options: {},
    },
    /** Subscribe to account button click events */
    subscribeToAccountButtonClicked: {
      name: "subscribeToAccountButtonClicked",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: true,
      options: {},
    },
    /** Subscribe to settings button clicked events */
    subscribeToSettingsButtonClicked: {
      name: "subscribeToSettingsButtonClicked",
      requestType: WebviewProviderTypeRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: true,
      options: {},
    },
    /** Subscribe to partial message updates (streaming Cline messages as they're built) */
    subscribeToPartialMessage: {
      name: "subscribeToPartialMessage",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: ClineMessage,
      responseStream: true,
      options: {},
    },
    /** Subscribe to theme change events */
    subscribeToTheme: {
      name: "subscribeToTheme",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: String,
      responseStream: true,
      options: {},
    },
    /** Initialize webview when it launches */
    initializeWebview: {
      name: "initializeWebview",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {},
    },
    /** Subscribe to relinquish control events */
    subscribeToRelinquishControl: {
      name: "subscribeToRelinquishControl",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: true,
      options: {},
    },
    /** Subscribe to focus chat input events with client ID */
    subscribeToFocusChatInput: {
      name: "subscribeToFocusChatInput",
      requestType: StringRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: true,
      options: {},
    },
    /** Subscribe to webview visibility change events */
    subscribeToDidBecomeVisible: {
      name: "subscribeToDidBecomeVisible",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: true,
      options: {},
    },
    /** Returns the HTML for the webview index page. This is only used by external clients, not by the vscode webview. */
    getWebviewHtml: {
      name: "getWebviewHtml",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: String,
      responseStream: false,
      options: {},
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
