// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js

// Import all method implementations
import { registerMethod } from "./index"
import { copyToClipboard } from "./copyToClipboard"
import { createRuleFile } from "./createRuleFile"
import { deleteRuleFile } from "./deleteRuleFile"
import { getRelativePaths } from "./getRelativePaths"
import { openFile } from "./openFile"
import { openImage } from "./openImage"
import { openMention } from "./openMention"
import { openTaskHistory } from "./openTaskHistory"
import { refreshRules } from "./refreshRules"
import { searchCommits } from "./searchCommits"
import { searchFiles } from "./searchFiles"
import { selectFiles } from "./selectFiles"
import { selectImages } from "./selectImages"
import { subscribeToWorkspaceUpdates } from "./subscribeToWorkspaceUpdates"
import { toggleClineRule } from "./toggleClineRule"
import { toggleCursorRule } from "./toggleCursorRule"
import { toggleWindsurfRule } from "./toggleWindsurfRule"
import { toggleWorkflow } from "./toggleWorkflow"

// Streaming methods for this service
export const streamingMethods = [
  "subscribeToWorkspaceUpdates"
]

// Register all file service methods
export function registerAllMethods(): void {
	// Register each method with the registry
	registerMethod("copyToClipboard", copyToClipboard)
	registerMethod("createRuleFile", createRuleFile)
	registerMethod("deleteRuleFile", deleteRuleFile)
	registerMethod("getRelativePaths", getRelativePaths)
	registerMethod("openFile", openFile)
	registerMethod("openImage", openImage)
	registerMethod("openMention", openMention)
	registerMethod("openTaskHistory", openTaskHistory)
	registerMethod("refreshRules", refreshRules)
	registerMethod("searchCommits", searchCommits)
	registerMethod("searchFiles", searchFiles)
	registerMethod("selectFiles", selectFiles)
	registerMethod("selectImages", selectImages)
	registerMethod("subscribeToWorkspaceUpdates", subscribeToWorkspaceUpdates, { isStreaming: true })
	registerMethod("toggleClineRule", toggleClineRule)
	registerMethod("toggleCursorRule", toggleCursorRule)
	registerMethod("toggleWindsurfRule", toggleWindsurfRule)
	registerMethod("toggleWorkflow", toggleWorkflow)
}