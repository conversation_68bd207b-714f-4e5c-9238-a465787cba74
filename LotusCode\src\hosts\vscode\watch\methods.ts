// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js

// Import all method implementations
import { registerMethod } from "./index"
import { subscribeToFile } from "./subscribeToFile"

// Streaming methods for this service
export const streamingMethods = [
  "subscribeToFile"
]

// Register all watch service methods
export function registerAllMethods(): void {
	// Register each method with the registry
	registerMethod("subscribeToFile", subscribeToFile, { isStreaming: true })
}