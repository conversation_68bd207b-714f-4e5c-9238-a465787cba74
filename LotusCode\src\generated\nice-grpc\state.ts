// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: state.proto

/* eslint-disable */
import { Binary<PERSON>eader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { type CallContext, type CallOptions } from "nice-grpc-common";
import { Boolean, BooleanRequest, Empty, EmptyRequest, Int64, Int64Request, Metadata, StringRequest } from "./common";

export enum PlanActMode {
  PLAN = 0,
  ACT = 1,
  UNRECOGNIZED = -1,
}

export function planActModeFromJSON(object: any): PlanActMode {
  switch (object) {
    case 0:
    case "PLAN":
      return PlanActMode.PLAN;
    case 1:
    case "ACT":
      return PlanActMode.ACT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PlanActMode.UNRECOGNIZED;
  }
}

export function planActModeToJSON(object: PlanActMode): string {
  switch (object) {
    case PlanActMode.PLAN:
      return "PLAN";
    case PlanActMode.ACT:
      return "ACT";
    case PlanActMode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface State {
  stateJson: string;
}

export interface TerminalProfiles {
  profiles: TerminalProfile[];
}

export interface TerminalProfile {
  id: string;
  name: string;
  path?: string | undefined;
  description?: string | undefined;
}

export interface TerminalProfileUpdateResponse {
  closedCount: number;
  busyTerminalsCount: number;
  hasBusyTerminals: boolean;
}

export interface TogglePlanActModeRequest {
  metadata?: Metadata | undefined;
  chatSettings?: ChatSettings | undefined;
  chatContent?: ChatContent | undefined;
}

export interface ChatSettings {
  mode: PlanActMode;
  preferredLanguage?: string | undefined;
  openAiReasoningEffort?: string | undefined;
}

export interface ChatContent {
  message?: string | undefined;
  images: string[];
  files: string[];
}

export interface ResetStateRequest {
  metadata?: Metadata | undefined;
  global?: boolean | undefined;
}

export interface AutoApprovalSettingsRequest {
  metadata?: Metadata | undefined;
  version: number;
  enabled: boolean;
  actions?: AutoApprovalSettingsRequest_Actions | undefined;
  maxRequests: number;
  enableNotifications: boolean;
  favorites: string[];
}

export interface AutoApprovalSettingsRequest_Actions {
  readFiles: boolean;
  readFilesExternally: boolean;
  editFiles: boolean;
  editFilesExternally: boolean;
  executeSafeCommands: boolean;
  executeAllCommands: boolean;
  useBrowser: boolean;
  useMcp: boolean;
}

/** Message for updating settings */
export interface UpdateSettingsRequest {
  metadata?: Metadata | undefined;
  apiConfiguration?: ApiConfiguration | undefined;
  telemetrySetting?: string | undefined;
  planActSeparateModelsSetting?: boolean | undefined;
  enableCheckpointsSetting?: boolean | undefined;
  mcpMarketplaceEnabled?: boolean | undefined;
  chatSettings?: ChatSettings | undefined;
  shellIntegrationTimeout?: number | undefined;
  terminalReuseEnabled?: boolean | undefined;
  mcpResponsesCollapsed?: boolean | undefined;
  mcpRichDisplayEnabled?: boolean | undefined;
  terminalOutputLineLimit?: number | undefined;
}

/** Complete API Configuration message */
export interface ApiConfiguration {
  /** Core API fields */
  apiProvider?: string | undefined;
  apiModelId?:
    | string
    | undefined;
  /** anthropic */
  apiKey?: string | undefined;
  apiBaseUrl?:
    | string
    | undefined;
  /** Provider-specific API keys */
  clineApiKey?: string | undefined;
  openrouterApiKey?: string | undefined;
  anthropicBaseUrl?: string | undefined;
  openaiApiKey?: string | undefined;
  openaiNativeApiKey?: string | undefined;
  geminiApiKey?: string | undefined;
  deepseekApiKey?: string | undefined;
  requestyApiKey?: string | undefined;
  togetherApiKey?: string | undefined;
  fireworksApiKey?: string | undefined;
  qwenApiKey?: string | undefined;
  doubaoApiKey?: string | undefined;
  mistralApiKey?: string | undefined;
  nebiusApiKey?: string | undefined;
  asksageApiKey?: string | undefined;
  xaiApiKey?: string | undefined;
  sambanovaApiKey?: string | undefined;
  cerebrasApiKey?:
    | string
    | undefined;
  /** Model IDs */
  openrouterModelId?: string | undefined;
  openaiModelId?: string | undefined;
  anthropicModelId?: string | undefined;
  bedrockModelId?: string | undefined;
  vertexModelId?: string | undefined;
  geminiModelId?: string | undefined;
  ollamaModelId?: string | undefined;
  lmStudioModelId?: string | undefined;
  litellmModelId?: string | undefined;
  requestyModelId?: string | undefined;
  togetherModelId?: string | undefined;
  fireworksModelId?:
    | string
    | undefined;
  /** AWS Bedrock fields */
  awsBedrockCustomSelected?: boolean | undefined;
  awsBedrockCustomModelBaseId?: string | undefined;
  awsAccessKey?: string | undefined;
  awsSecretKey?: string | undefined;
  awsSessionToken?: string | undefined;
  awsRegion?: string | undefined;
  awsUseCrossRegionInference?: boolean | undefined;
  awsBedrockUsePromptCache?: boolean | undefined;
  awsUseProfile?: boolean | undefined;
  awsProfile?: string | undefined;
  awsBedrockEndpoint?:
    | string
    | undefined;
  /** Vertex AI fields */
  vertexProjectId?: string | undefined;
  vertexRegion?:
    | string
    | undefined;
  /** Base URLs and endpoints */
  openaiBaseUrl?: string | undefined;
  ollamaBaseUrl?: string | undefined;
  lmStudioBaseUrl?: string | undefined;
  geminiBaseUrl?: string | undefined;
  litellmBaseUrl?: string | undefined;
  asksageApiUrl?:
    | string
    | undefined;
  /** LiteLLM specific fields */
  litellmApiKey?: string | undefined;
  litellmUsePromptCache?:
    | boolean
    | undefined;
  /** Model configuration */
  thinkingBudgetTokens?: number | undefined;
  reasoningEffort?: string | undefined;
  requestTimeoutMs?:
    | number
    | undefined;
  /** Fireworks specific */
  fireworksModelMaxCompletionTokens?: number | undefined;
  fireworksModelMaxTokens?:
    | number
    | undefined;
  /** Azure specific */
  azureApiVersion?:
    | string
    | undefined;
  /** Ollama specific */
  ollamaApiOptionsCtxNum?:
    | string
    | undefined;
  /** Qwen specific */
  qwenApiLine?:
    | string
    | undefined;
  /** OpenRouter specific */
  openrouterProviderSorting?:
    | string
    | undefined;
  /** VSCode LM (stored as JSON string due to complex type) */
  vscodeLmModelSelector?:
    | string
    | undefined;
  /** Model info objects (stored as JSON strings) */
  openrouterModelInfo?: string | undefined;
  openaiModelInfo?: string | undefined;
  requestyModelInfo?: string | undefined;
  litellmModelInfo?:
    | string
    | undefined;
  /** OpenAI headers (stored as JSON string) */
  openaiHeaders?:
    | string
    | undefined;
  /** Favorited model IDs */
  favoritedModelIds: string[];
  /** SAP AI Core specific */
  sapAiCoreClientId?: string | undefined;
  sapAiCoreClientSecret?: string | undefined;
  sapAiCoreBaseUrl?: string | undefined;
  sapAiCoreTokenUrl?: string | undefined;
  sapAiResourceGroup?:
    | string
    | undefined;
  /** Claude Code specific */
  claudeCodePath?: string | undefined;
}

function createBaseState(): State {
  return { stateJson: "" };
}

export const State: MessageFns<State> = {
  encode(message: State, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.stateJson !== "") {
      writer.uint32(10).string(message.stateJson);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): State {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.stateJson = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): State {
    return { stateJson: isSet(object.stateJson) ? globalThis.String(object.stateJson) : "" };
  },

  toJSON(message: State): unknown {
    const obj: any = {};
    if (message.stateJson !== "") {
      obj.stateJson = message.stateJson;
    }
    return obj;
  },

  create(base?: DeepPartial<State>): State {
    return State.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<State>): State {
    const message = createBaseState();
    message.stateJson = object.stateJson ?? "";
    return message;
  },
};

function createBaseTerminalProfiles(): TerminalProfiles {
  return { profiles: [] };
}

export const TerminalProfiles: MessageFns<TerminalProfiles> = {
  encode(message: TerminalProfiles, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.profiles) {
      TerminalProfile.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TerminalProfiles {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTerminalProfiles();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.profiles.push(TerminalProfile.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TerminalProfiles {
    return {
      profiles: globalThis.Array.isArray(object?.profiles)
        ? object.profiles.map((e: any) => TerminalProfile.fromJSON(e))
        : [],
    };
  },

  toJSON(message: TerminalProfiles): unknown {
    const obj: any = {};
    if (message.profiles?.length) {
      obj.profiles = message.profiles.map((e) => TerminalProfile.toJSON(e));
    }
    return obj;
  },

  create(base?: DeepPartial<TerminalProfiles>): TerminalProfiles {
    return TerminalProfiles.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<TerminalProfiles>): TerminalProfiles {
    const message = createBaseTerminalProfiles();
    message.profiles = object.profiles?.map((e) => TerminalProfile.fromPartial(e)) || [];
    return message;
  },
};

function createBaseTerminalProfile(): TerminalProfile {
  return { id: "", name: "", path: undefined, description: undefined };
}

export const TerminalProfile: MessageFns<TerminalProfile> = {
  encode(message: TerminalProfile, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.path !== undefined) {
      writer.uint32(26).string(message.path);
    }
    if (message.description !== undefined) {
      writer.uint32(34).string(message.description);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TerminalProfile {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTerminalProfile();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.path = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.description = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TerminalProfile {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      path: isSet(object.path) ? globalThis.String(object.path) : undefined,
      description: isSet(object.description) ? globalThis.String(object.description) : undefined,
    };
  },

  toJSON(message: TerminalProfile): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.path !== undefined) {
      obj.path = message.path;
    }
    if (message.description !== undefined) {
      obj.description = message.description;
    }
    return obj;
  },

  create(base?: DeepPartial<TerminalProfile>): TerminalProfile {
    return TerminalProfile.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<TerminalProfile>): TerminalProfile {
    const message = createBaseTerminalProfile();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.path = object.path ?? undefined;
    message.description = object.description ?? undefined;
    return message;
  },
};

function createBaseTerminalProfileUpdateResponse(): TerminalProfileUpdateResponse {
  return { closedCount: 0, busyTerminalsCount: 0, hasBusyTerminals: false };
}

export const TerminalProfileUpdateResponse: MessageFns<TerminalProfileUpdateResponse> = {
  encode(message: TerminalProfileUpdateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.closedCount !== 0) {
      writer.uint32(8).int32(message.closedCount);
    }
    if (message.busyTerminalsCount !== 0) {
      writer.uint32(16).int32(message.busyTerminalsCount);
    }
    if (message.hasBusyTerminals !== false) {
      writer.uint32(24).bool(message.hasBusyTerminals);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TerminalProfileUpdateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTerminalProfileUpdateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.closedCount = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.busyTerminalsCount = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.hasBusyTerminals = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TerminalProfileUpdateResponse {
    return {
      closedCount: isSet(object.closedCount) ? globalThis.Number(object.closedCount) : 0,
      busyTerminalsCount: isSet(object.busyTerminalsCount) ? globalThis.Number(object.busyTerminalsCount) : 0,
      hasBusyTerminals: isSet(object.hasBusyTerminals) ? globalThis.Boolean(object.hasBusyTerminals) : false,
    };
  },

  toJSON(message: TerminalProfileUpdateResponse): unknown {
    const obj: any = {};
    if (message.closedCount !== 0) {
      obj.closedCount = Math.round(message.closedCount);
    }
    if (message.busyTerminalsCount !== 0) {
      obj.busyTerminalsCount = Math.round(message.busyTerminalsCount);
    }
    if (message.hasBusyTerminals !== false) {
      obj.hasBusyTerminals = message.hasBusyTerminals;
    }
    return obj;
  },

  create(base?: DeepPartial<TerminalProfileUpdateResponse>): TerminalProfileUpdateResponse {
    return TerminalProfileUpdateResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<TerminalProfileUpdateResponse>): TerminalProfileUpdateResponse {
    const message = createBaseTerminalProfileUpdateResponse();
    message.closedCount = object.closedCount ?? 0;
    message.busyTerminalsCount = object.busyTerminalsCount ?? 0;
    message.hasBusyTerminals = object.hasBusyTerminals ?? false;
    return message;
  },
};

function createBaseTogglePlanActModeRequest(): TogglePlanActModeRequest {
  return { metadata: undefined, chatSettings: undefined, chatContent: undefined };
}

export const TogglePlanActModeRequest: MessageFns<TogglePlanActModeRequest> = {
  encode(message: TogglePlanActModeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join();
    }
    if (message.chatSettings !== undefined) {
      ChatSettings.encode(message.chatSettings, writer.uint32(18).fork()).join();
    }
    if (message.chatContent !== undefined) {
      ChatContent.encode(message.chatContent, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TogglePlanActModeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTogglePlanActModeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.metadata = Metadata.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.chatSettings = ChatSettings.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.chatContent = ChatContent.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TogglePlanActModeRequest {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      chatSettings: isSet(object.chatSettings) ? ChatSettings.fromJSON(object.chatSettings) : undefined,
      chatContent: isSet(object.chatContent) ? ChatContent.fromJSON(object.chatContent) : undefined,
    };
  },

  toJSON(message: TogglePlanActModeRequest): unknown {
    const obj: any = {};
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata);
    }
    if (message.chatSettings !== undefined) {
      obj.chatSettings = ChatSettings.toJSON(message.chatSettings);
    }
    if (message.chatContent !== undefined) {
      obj.chatContent = ChatContent.toJSON(message.chatContent);
    }
    return obj;
  },

  create(base?: DeepPartial<TogglePlanActModeRequest>): TogglePlanActModeRequest {
    return TogglePlanActModeRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<TogglePlanActModeRequest>): TogglePlanActModeRequest {
    const message = createBaseTogglePlanActModeRequest();
    message.metadata = (object.metadata !== undefined && object.metadata !== null)
      ? Metadata.fromPartial(object.metadata)
      : undefined;
    message.chatSettings = (object.chatSettings !== undefined && object.chatSettings !== null)
      ? ChatSettings.fromPartial(object.chatSettings)
      : undefined;
    message.chatContent = (object.chatContent !== undefined && object.chatContent !== null)
      ? ChatContent.fromPartial(object.chatContent)
      : undefined;
    return message;
  },
};

function createBaseChatSettings(): ChatSettings {
  return { mode: 0, preferredLanguage: undefined, openAiReasoningEffort: undefined };
}

export const ChatSettings: MessageFns<ChatSettings> = {
  encode(message: ChatSettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.mode !== 0) {
      writer.uint32(8).int32(message.mode);
    }
    if (message.preferredLanguage !== undefined) {
      writer.uint32(18).string(message.preferredLanguage);
    }
    if (message.openAiReasoningEffort !== undefined) {
      writer.uint32(26).string(message.openAiReasoningEffort);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChatSettings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChatSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.mode = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.preferredLanguage = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.openAiReasoningEffort = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ChatSettings {
    return {
      mode: isSet(object.mode) ? planActModeFromJSON(object.mode) : 0,
      preferredLanguage: isSet(object.preferredLanguage) ? globalThis.String(object.preferredLanguage) : undefined,
      openAiReasoningEffort: isSet(object.openAiReasoningEffort)
        ? globalThis.String(object.openAiReasoningEffort)
        : undefined,
    };
  },

  toJSON(message: ChatSettings): unknown {
    const obj: any = {};
    if (message.mode !== 0) {
      obj.mode = planActModeToJSON(message.mode);
    }
    if (message.preferredLanguage !== undefined) {
      obj.preferredLanguage = message.preferredLanguage;
    }
    if (message.openAiReasoningEffort !== undefined) {
      obj.openAiReasoningEffort = message.openAiReasoningEffort;
    }
    return obj;
  },

  create(base?: DeepPartial<ChatSettings>): ChatSettings {
    return ChatSettings.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ChatSettings>): ChatSettings {
    const message = createBaseChatSettings();
    message.mode = object.mode ?? 0;
    message.preferredLanguage = object.preferredLanguage ?? undefined;
    message.openAiReasoningEffort = object.openAiReasoningEffort ?? undefined;
    return message;
  },
};

function createBaseChatContent(): ChatContent {
  return { message: undefined, images: [], files: [] };
}

export const ChatContent: MessageFns<ChatContent> = {
  encode(message: ChatContent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.message !== undefined) {
      writer.uint32(10).string(message.message);
    }
    for (const v of message.images) {
      writer.uint32(18).string(v!);
    }
    for (const v of message.files) {
      writer.uint32(26).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChatContent {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChatContent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.images.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.files.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ChatContent {
    return {
      message: isSet(object.message) ? globalThis.String(object.message) : undefined,
      images: globalThis.Array.isArray(object?.images) ? object.images.map((e: any) => globalThis.String(e)) : [],
      files: globalThis.Array.isArray(object?.files) ? object.files.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: ChatContent): unknown {
    const obj: any = {};
    if (message.message !== undefined) {
      obj.message = message.message;
    }
    if (message.images?.length) {
      obj.images = message.images;
    }
    if (message.files?.length) {
      obj.files = message.files;
    }
    return obj;
  },

  create(base?: DeepPartial<ChatContent>): ChatContent {
    return ChatContent.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ChatContent>): ChatContent {
    const message = createBaseChatContent();
    message.message = object.message ?? undefined;
    message.images = object.images?.map((e) => e) || [];
    message.files = object.files?.map((e) => e) || [];
    return message;
  },
};

function createBaseResetStateRequest(): ResetStateRequest {
  return { metadata: undefined, global: undefined };
}

export const ResetStateRequest: MessageFns<ResetStateRequest> = {
  encode(message: ResetStateRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join();
    }
    if (message.global !== undefined) {
      writer.uint32(16).bool(message.global);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ResetStateRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResetStateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.metadata = Metadata.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.global = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ResetStateRequest {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      global: isSet(object.global) ? globalThis.Boolean(object.global) : undefined,
    };
  },

  toJSON(message: ResetStateRequest): unknown {
    const obj: any = {};
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata);
    }
    if (message.global !== undefined) {
      obj.global = message.global;
    }
    return obj;
  },

  create(base?: DeepPartial<ResetStateRequest>): ResetStateRequest {
    return ResetStateRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ResetStateRequest>): ResetStateRequest {
    const message = createBaseResetStateRequest();
    message.metadata = (object.metadata !== undefined && object.metadata !== null)
      ? Metadata.fromPartial(object.metadata)
      : undefined;
    message.global = object.global ?? undefined;
    return message;
  },
};

function createBaseAutoApprovalSettingsRequest(): AutoApprovalSettingsRequest {
  return {
    metadata: undefined,
    version: 0,
    enabled: false,
    actions: undefined,
    maxRequests: 0,
    enableNotifications: false,
    favorites: [],
  };
}

export const AutoApprovalSettingsRequest: MessageFns<AutoApprovalSettingsRequest> = {
  encode(message: AutoApprovalSettingsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join();
    }
    if (message.version !== 0) {
      writer.uint32(16).int32(message.version);
    }
    if (message.enabled !== false) {
      writer.uint32(24).bool(message.enabled);
    }
    if (message.actions !== undefined) {
      AutoApprovalSettingsRequest_Actions.encode(message.actions, writer.uint32(34).fork()).join();
    }
    if (message.maxRequests !== 0) {
      writer.uint32(40).int32(message.maxRequests);
    }
    if (message.enableNotifications !== false) {
      writer.uint32(48).bool(message.enableNotifications);
    }
    for (const v of message.favorites) {
      writer.uint32(58).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AutoApprovalSettingsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAutoApprovalSettingsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.metadata = Metadata.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.version = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.enabled = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.actions = AutoApprovalSettingsRequest_Actions.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.maxRequests = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.enableNotifications = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.favorites.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AutoApprovalSettingsRequest {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      enabled: isSet(object.enabled) ? globalThis.Boolean(object.enabled) : false,
      actions: isSet(object.actions) ? AutoApprovalSettingsRequest_Actions.fromJSON(object.actions) : undefined,
      maxRequests: isSet(object.maxRequests) ? globalThis.Number(object.maxRequests) : 0,
      enableNotifications: isSet(object.enableNotifications) ? globalThis.Boolean(object.enableNotifications) : false,
      favorites: globalThis.Array.isArray(object?.favorites)
        ? object.favorites.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: AutoApprovalSettingsRequest): unknown {
    const obj: any = {};
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata);
    }
    if (message.version !== 0) {
      obj.version = Math.round(message.version);
    }
    if (message.enabled !== false) {
      obj.enabled = message.enabled;
    }
    if (message.actions !== undefined) {
      obj.actions = AutoApprovalSettingsRequest_Actions.toJSON(message.actions);
    }
    if (message.maxRequests !== 0) {
      obj.maxRequests = Math.round(message.maxRequests);
    }
    if (message.enableNotifications !== false) {
      obj.enableNotifications = message.enableNotifications;
    }
    if (message.favorites?.length) {
      obj.favorites = message.favorites;
    }
    return obj;
  },

  create(base?: DeepPartial<AutoApprovalSettingsRequest>): AutoApprovalSettingsRequest {
    return AutoApprovalSettingsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<AutoApprovalSettingsRequest>): AutoApprovalSettingsRequest {
    const message = createBaseAutoApprovalSettingsRequest();
    message.metadata = (object.metadata !== undefined && object.metadata !== null)
      ? Metadata.fromPartial(object.metadata)
      : undefined;
    message.version = object.version ?? 0;
    message.enabled = object.enabled ?? false;
    message.actions = (object.actions !== undefined && object.actions !== null)
      ? AutoApprovalSettingsRequest_Actions.fromPartial(object.actions)
      : undefined;
    message.maxRequests = object.maxRequests ?? 0;
    message.enableNotifications = object.enableNotifications ?? false;
    message.favorites = object.favorites?.map((e) => e) || [];
    return message;
  },
};

function createBaseAutoApprovalSettingsRequest_Actions(): AutoApprovalSettingsRequest_Actions {
  return {
    readFiles: false,
    readFilesExternally: false,
    editFiles: false,
    editFilesExternally: false,
    executeSafeCommands: false,
    executeAllCommands: false,
    useBrowser: false,
    useMcp: false,
  };
}

export const AutoApprovalSettingsRequest_Actions: MessageFns<AutoApprovalSettingsRequest_Actions> = {
  encode(message: AutoApprovalSettingsRequest_Actions, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.readFiles !== false) {
      writer.uint32(8).bool(message.readFiles);
    }
    if (message.readFilesExternally !== false) {
      writer.uint32(16).bool(message.readFilesExternally);
    }
    if (message.editFiles !== false) {
      writer.uint32(24).bool(message.editFiles);
    }
    if (message.editFilesExternally !== false) {
      writer.uint32(32).bool(message.editFilesExternally);
    }
    if (message.executeSafeCommands !== false) {
      writer.uint32(40).bool(message.executeSafeCommands);
    }
    if (message.executeAllCommands !== false) {
      writer.uint32(48).bool(message.executeAllCommands);
    }
    if (message.useBrowser !== false) {
      writer.uint32(56).bool(message.useBrowser);
    }
    if (message.useMcp !== false) {
      writer.uint32(64).bool(message.useMcp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AutoApprovalSettingsRequest_Actions {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAutoApprovalSettingsRequest_Actions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.readFiles = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.readFilesExternally = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.editFiles = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.editFilesExternally = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.executeSafeCommands = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.executeAllCommands = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.useBrowser = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.useMcp = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AutoApprovalSettingsRequest_Actions {
    return {
      readFiles: isSet(object.readFiles) ? globalThis.Boolean(object.readFiles) : false,
      readFilesExternally: isSet(object.readFilesExternally) ? globalThis.Boolean(object.readFilesExternally) : false,
      editFiles: isSet(object.editFiles) ? globalThis.Boolean(object.editFiles) : false,
      editFilesExternally: isSet(object.editFilesExternally) ? globalThis.Boolean(object.editFilesExternally) : false,
      executeSafeCommands: isSet(object.executeSafeCommands) ? globalThis.Boolean(object.executeSafeCommands) : false,
      executeAllCommands: isSet(object.executeAllCommands) ? globalThis.Boolean(object.executeAllCommands) : false,
      useBrowser: isSet(object.useBrowser) ? globalThis.Boolean(object.useBrowser) : false,
      useMcp: isSet(object.useMcp) ? globalThis.Boolean(object.useMcp) : false,
    };
  },

  toJSON(message: AutoApprovalSettingsRequest_Actions): unknown {
    const obj: any = {};
    if (message.readFiles !== false) {
      obj.readFiles = message.readFiles;
    }
    if (message.readFilesExternally !== false) {
      obj.readFilesExternally = message.readFilesExternally;
    }
    if (message.editFiles !== false) {
      obj.editFiles = message.editFiles;
    }
    if (message.editFilesExternally !== false) {
      obj.editFilesExternally = message.editFilesExternally;
    }
    if (message.executeSafeCommands !== false) {
      obj.executeSafeCommands = message.executeSafeCommands;
    }
    if (message.executeAllCommands !== false) {
      obj.executeAllCommands = message.executeAllCommands;
    }
    if (message.useBrowser !== false) {
      obj.useBrowser = message.useBrowser;
    }
    if (message.useMcp !== false) {
      obj.useMcp = message.useMcp;
    }
    return obj;
  },

  create(base?: DeepPartial<AutoApprovalSettingsRequest_Actions>): AutoApprovalSettingsRequest_Actions {
    return AutoApprovalSettingsRequest_Actions.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<AutoApprovalSettingsRequest_Actions>): AutoApprovalSettingsRequest_Actions {
    const message = createBaseAutoApprovalSettingsRequest_Actions();
    message.readFiles = object.readFiles ?? false;
    message.readFilesExternally = object.readFilesExternally ?? false;
    message.editFiles = object.editFiles ?? false;
    message.editFilesExternally = object.editFilesExternally ?? false;
    message.executeSafeCommands = object.executeSafeCommands ?? false;
    message.executeAllCommands = object.executeAllCommands ?? false;
    message.useBrowser = object.useBrowser ?? false;
    message.useMcp = object.useMcp ?? false;
    return message;
  },
};

function createBaseUpdateSettingsRequest(): UpdateSettingsRequest {
  return {
    metadata: undefined,
    apiConfiguration: undefined,
    telemetrySetting: undefined,
    planActSeparateModelsSetting: undefined,
    enableCheckpointsSetting: undefined,
    mcpMarketplaceEnabled: undefined,
    chatSettings: undefined,
    shellIntegrationTimeout: undefined,
    terminalReuseEnabled: undefined,
    mcpResponsesCollapsed: undefined,
    mcpRichDisplayEnabled: undefined,
    terminalOutputLineLimit: undefined,
  };
}

export const UpdateSettingsRequest: MessageFns<UpdateSettingsRequest> = {
  encode(message: UpdateSettingsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join();
    }
    if (message.apiConfiguration !== undefined) {
      ApiConfiguration.encode(message.apiConfiguration, writer.uint32(18).fork()).join();
    }
    if (message.telemetrySetting !== undefined) {
      writer.uint32(26).string(message.telemetrySetting);
    }
    if (message.planActSeparateModelsSetting !== undefined) {
      writer.uint32(32).bool(message.planActSeparateModelsSetting);
    }
    if (message.enableCheckpointsSetting !== undefined) {
      writer.uint32(40).bool(message.enableCheckpointsSetting);
    }
    if (message.mcpMarketplaceEnabled !== undefined) {
      writer.uint32(48).bool(message.mcpMarketplaceEnabled);
    }
    if (message.chatSettings !== undefined) {
      ChatSettings.encode(message.chatSettings, writer.uint32(58).fork()).join();
    }
    if (message.shellIntegrationTimeout !== undefined) {
      writer.uint32(64).int64(message.shellIntegrationTimeout);
    }
    if (message.terminalReuseEnabled !== undefined) {
      writer.uint32(72).bool(message.terminalReuseEnabled);
    }
    if (message.mcpResponsesCollapsed !== undefined) {
      writer.uint32(80).bool(message.mcpResponsesCollapsed);
    }
    if (message.mcpRichDisplayEnabled !== undefined) {
      writer.uint32(88).bool(message.mcpRichDisplayEnabled);
    }
    if (message.terminalOutputLineLimit !== undefined) {
      writer.uint32(96).int64(message.terminalOutputLineLimit);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateSettingsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateSettingsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.metadata = Metadata.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.apiConfiguration = ApiConfiguration.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.telemetrySetting = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.planActSeparateModelsSetting = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.enableCheckpointsSetting = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.mcpMarketplaceEnabled = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.chatSettings = ChatSettings.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.shellIntegrationTimeout = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.terminalReuseEnabled = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.mcpResponsesCollapsed = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.mcpRichDisplayEnabled = reader.bool();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.terminalOutputLineLimit = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateSettingsRequest {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      apiConfiguration: isSet(object.apiConfiguration) ? ApiConfiguration.fromJSON(object.apiConfiguration) : undefined,
      telemetrySetting: isSet(object.telemetrySetting) ? globalThis.String(object.telemetrySetting) : undefined,
      planActSeparateModelsSetting: isSet(object.planActSeparateModelsSetting)
        ? globalThis.Boolean(object.planActSeparateModelsSetting)
        : undefined,
      enableCheckpointsSetting: isSet(object.enableCheckpointsSetting)
        ? globalThis.Boolean(object.enableCheckpointsSetting)
        : undefined,
      mcpMarketplaceEnabled: isSet(object.mcpMarketplaceEnabled)
        ? globalThis.Boolean(object.mcpMarketplaceEnabled)
        : undefined,
      chatSettings: isSet(object.chatSettings) ? ChatSettings.fromJSON(object.chatSettings) : undefined,
      shellIntegrationTimeout: isSet(object.shellIntegrationTimeout)
        ? globalThis.Number(object.shellIntegrationTimeout)
        : undefined,
      terminalReuseEnabled: isSet(object.terminalReuseEnabled)
        ? globalThis.Boolean(object.terminalReuseEnabled)
        : undefined,
      mcpResponsesCollapsed: isSet(object.mcpResponsesCollapsed)
        ? globalThis.Boolean(object.mcpResponsesCollapsed)
        : undefined,
      mcpRichDisplayEnabled: isSet(object.mcpRichDisplayEnabled)
        ? globalThis.Boolean(object.mcpRichDisplayEnabled)
        : undefined,
      terminalOutputLineLimit: isSet(object.terminalOutputLineLimit)
        ? globalThis.Number(object.terminalOutputLineLimit)
        : undefined,
    };
  },

  toJSON(message: UpdateSettingsRequest): unknown {
    const obj: any = {};
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata);
    }
    if (message.apiConfiguration !== undefined) {
      obj.apiConfiguration = ApiConfiguration.toJSON(message.apiConfiguration);
    }
    if (message.telemetrySetting !== undefined) {
      obj.telemetrySetting = message.telemetrySetting;
    }
    if (message.planActSeparateModelsSetting !== undefined) {
      obj.planActSeparateModelsSetting = message.planActSeparateModelsSetting;
    }
    if (message.enableCheckpointsSetting !== undefined) {
      obj.enableCheckpointsSetting = message.enableCheckpointsSetting;
    }
    if (message.mcpMarketplaceEnabled !== undefined) {
      obj.mcpMarketplaceEnabled = message.mcpMarketplaceEnabled;
    }
    if (message.chatSettings !== undefined) {
      obj.chatSettings = ChatSettings.toJSON(message.chatSettings);
    }
    if (message.shellIntegrationTimeout !== undefined) {
      obj.shellIntegrationTimeout = Math.round(message.shellIntegrationTimeout);
    }
    if (message.terminalReuseEnabled !== undefined) {
      obj.terminalReuseEnabled = message.terminalReuseEnabled;
    }
    if (message.mcpResponsesCollapsed !== undefined) {
      obj.mcpResponsesCollapsed = message.mcpResponsesCollapsed;
    }
    if (message.mcpRichDisplayEnabled !== undefined) {
      obj.mcpRichDisplayEnabled = message.mcpRichDisplayEnabled;
    }
    if (message.terminalOutputLineLimit !== undefined) {
      obj.terminalOutputLineLimit = Math.round(message.terminalOutputLineLimit);
    }
    return obj;
  },

  create(base?: DeepPartial<UpdateSettingsRequest>): UpdateSettingsRequest {
    return UpdateSettingsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpdateSettingsRequest>): UpdateSettingsRequest {
    const message = createBaseUpdateSettingsRequest();
    message.metadata = (object.metadata !== undefined && object.metadata !== null)
      ? Metadata.fromPartial(object.metadata)
      : undefined;
    message.apiConfiguration = (object.apiConfiguration !== undefined && object.apiConfiguration !== null)
      ? ApiConfiguration.fromPartial(object.apiConfiguration)
      : undefined;
    message.telemetrySetting = object.telemetrySetting ?? undefined;
    message.planActSeparateModelsSetting = object.planActSeparateModelsSetting ?? undefined;
    message.enableCheckpointsSetting = object.enableCheckpointsSetting ?? undefined;
    message.mcpMarketplaceEnabled = object.mcpMarketplaceEnabled ?? undefined;
    message.chatSettings = (object.chatSettings !== undefined && object.chatSettings !== null)
      ? ChatSettings.fromPartial(object.chatSettings)
      : undefined;
    message.shellIntegrationTimeout = object.shellIntegrationTimeout ?? undefined;
    message.terminalReuseEnabled = object.terminalReuseEnabled ?? undefined;
    message.mcpResponsesCollapsed = object.mcpResponsesCollapsed ?? undefined;
    message.mcpRichDisplayEnabled = object.mcpRichDisplayEnabled ?? undefined;
    message.terminalOutputLineLimit = object.terminalOutputLineLimit ?? undefined;
    return message;
  },
};

function createBaseApiConfiguration(): ApiConfiguration {
  return {
    apiProvider: undefined,
    apiModelId: undefined,
    apiKey: undefined,
    apiBaseUrl: undefined,
    clineApiKey: undefined,
    openrouterApiKey: undefined,
    anthropicBaseUrl: undefined,
    openaiApiKey: undefined,
    openaiNativeApiKey: undefined,
    geminiApiKey: undefined,
    deepseekApiKey: undefined,
    requestyApiKey: undefined,
    togetherApiKey: undefined,
    fireworksApiKey: undefined,
    qwenApiKey: undefined,
    doubaoApiKey: undefined,
    mistralApiKey: undefined,
    nebiusApiKey: undefined,
    asksageApiKey: undefined,
    xaiApiKey: undefined,
    sambanovaApiKey: undefined,
    cerebrasApiKey: undefined,
    openrouterModelId: undefined,
    openaiModelId: undefined,
    anthropicModelId: undefined,
    bedrockModelId: undefined,
    vertexModelId: undefined,
    geminiModelId: undefined,
    ollamaModelId: undefined,
    lmStudioModelId: undefined,
    litellmModelId: undefined,
    requestyModelId: undefined,
    togetherModelId: undefined,
    fireworksModelId: undefined,
    awsBedrockCustomSelected: undefined,
    awsBedrockCustomModelBaseId: undefined,
    awsAccessKey: undefined,
    awsSecretKey: undefined,
    awsSessionToken: undefined,
    awsRegion: undefined,
    awsUseCrossRegionInference: undefined,
    awsBedrockUsePromptCache: undefined,
    awsUseProfile: undefined,
    awsProfile: undefined,
    awsBedrockEndpoint: undefined,
    vertexProjectId: undefined,
    vertexRegion: undefined,
    openaiBaseUrl: undefined,
    ollamaBaseUrl: undefined,
    lmStudioBaseUrl: undefined,
    geminiBaseUrl: undefined,
    litellmBaseUrl: undefined,
    asksageApiUrl: undefined,
    litellmApiKey: undefined,
    litellmUsePromptCache: undefined,
    thinkingBudgetTokens: undefined,
    reasoningEffort: undefined,
    requestTimeoutMs: undefined,
    fireworksModelMaxCompletionTokens: undefined,
    fireworksModelMaxTokens: undefined,
    azureApiVersion: undefined,
    ollamaApiOptionsCtxNum: undefined,
    qwenApiLine: undefined,
    openrouterProviderSorting: undefined,
    vscodeLmModelSelector: undefined,
    openrouterModelInfo: undefined,
    openaiModelInfo: undefined,
    requestyModelInfo: undefined,
    litellmModelInfo: undefined,
    openaiHeaders: undefined,
    favoritedModelIds: [],
    sapAiCoreClientId: undefined,
    sapAiCoreClientSecret: undefined,
    sapAiCoreBaseUrl: undefined,
    sapAiCoreTokenUrl: undefined,
    sapAiResourceGroup: undefined,
    claudeCodePath: undefined,
  };
}

export const ApiConfiguration: MessageFns<ApiConfiguration> = {
  encode(message: ApiConfiguration, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.apiProvider !== undefined) {
      writer.uint32(10).string(message.apiProvider);
    }
    if (message.apiModelId !== undefined) {
      writer.uint32(18).string(message.apiModelId);
    }
    if (message.apiKey !== undefined) {
      writer.uint32(26).string(message.apiKey);
    }
    if (message.apiBaseUrl !== undefined) {
      writer.uint32(34).string(message.apiBaseUrl);
    }
    if (message.clineApiKey !== undefined) {
      writer.uint32(42).string(message.clineApiKey);
    }
    if (message.openrouterApiKey !== undefined) {
      writer.uint32(50).string(message.openrouterApiKey);
    }
    if (message.anthropicBaseUrl !== undefined) {
      writer.uint32(58).string(message.anthropicBaseUrl);
    }
    if (message.openaiApiKey !== undefined) {
      writer.uint32(66).string(message.openaiApiKey);
    }
    if (message.openaiNativeApiKey !== undefined) {
      writer.uint32(74).string(message.openaiNativeApiKey);
    }
    if (message.geminiApiKey !== undefined) {
      writer.uint32(82).string(message.geminiApiKey);
    }
    if (message.deepseekApiKey !== undefined) {
      writer.uint32(90).string(message.deepseekApiKey);
    }
    if (message.requestyApiKey !== undefined) {
      writer.uint32(98).string(message.requestyApiKey);
    }
    if (message.togetherApiKey !== undefined) {
      writer.uint32(106).string(message.togetherApiKey);
    }
    if (message.fireworksApiKey !== undefined) {
      writer.uint32(114).string(message.fireworksApiKey);
    }
    if (message.qwenApiKey !== undefined) {
      writer.uint32(122).string(message.qwenApiKey);
    }
    if (message.doubaoApiKey !== undefined) {
      writer.uint32(130).string(message.doubaoApiKey);
    }
    if (message.mistralApiKey !== undefined) {
      writer.uint32(138).string(message.mistralApiKey);
    }
    if (message.nebiusApiKey !== undefined) {
      writer.uint32(146).string(message.nebiusApiKey);
    }
    if (message.asksageApiKey !== undefined) {
      writer.uint32(154).string(message.asksageApiKey);
    }
    if (message.xaiApiKey !== undefined) {
      writer.uint32(162).string(message.xaiApiKey);
    }
    if (message.sambanovaApiKey !== undefined) {
      writer.uint32(170).string(message.sambanovaApiKey);
    }
    if (message.cerebrasApiKey !== undefined) {
      writer.uint32(178).string(message.cerebrasApiKey);
    }
    if (message.openrouterModelId !== undefined) {
      writer.uint32(186).string(message.openrouterModelId);
    }
    if (message.openaiModelId !== undefined) {
      writer.uint32(194).string(message.openaiModelId);
    }
    if (message.anthropicModelId !== undefined) {
      writer.uint32(202).string(message.anthropicModelId);
    }
    if (message.bedrockModelId !== undefined) {
      writer.uint32(210).string(message.bedrockModelId);
    }
    if (message.vertexModelId !== undefined) {
      writer.uint32(218).string(message.vertexModelId);
    }
    if (message.geminiModelId !== undefined) {
      writer.uint32(226).string(message.geminiModelId);
    }
    if (message.ollamaModelId !== undefined) {
      writer.uint32(234).string(message.ollamaModelId);
    }
    if (message.lmStudioModelId !== undefined) {
      writer.uint32(242).string(message.lmStudioModelId);
    }
    if (message.litellmModelId !== undefined) {
      writer.uint32(250).string(message.litellmModelId);
    }
    if (message.requestyModelId !== undefined) {
      writer.uint32(258).string(message.requestyModelId);
    }
    if (message.togetherModelId !== undefined) {
      writer.uint32(266).string(message.togetherModelId);
    }
    if (message.fireworksModelId !== undefined) {
      writer.uint32(274).string(message.fireworksModelId);
    }
    if (message.awsBedrockCustomSelected !== undefined) {
      writer.uint32(280).bool(message.awsBedrockCustomSelected);
    }
    if (message.awsBedrockCustomModelBaseId !== undefined) {
      writer.uint32(290).string(message.awsBedrockCustomModelBaseId);
    }
    if (message.awsAccessKey !== undefined) {
      writer.uint32(298).string(message.awsAccessKey);
    }
    if (message.awsSecretKey !== undefined) {
      writer.uint32(306).string(message.awsSecretKey);
    }
    if (message.awsSessionToken !== undefined) {
      writer.uint32(314).string(message.awsSessionToken);
    }
    if (message.awsRegion !== undefined) {
      writer.uint32(322).string(message.awsRegion);
    }
    if (message.awsUseCrossRegionInference !== undefined) {
      writer.uint32(328).bool(message.awsUseCrossRegionInference);
    }
    if (message.awsBedrockUsePromptCache !== undefined) {
      writer.uint32(336).bool(message.awsBedrockUsePromptCache);
    }
    if (message.awsUseProfile !== undefined) {
      writer.uint32(344).bool(message.awsUseProfile);
    }
    if (message.awsProfile !== undefined) {
      writer.uint32(354).string(message.awsProfile);
    }
    if (message.awsBedrockEndpoint !== undefined) {
      writer.uint32(362).string(message.awsBedrockEndpoint);
    }
    if (message.vertexProjectId !== undefined) {
      writer.uint32(370).string(message.vertexProjectId);
    }
    if (message.vertexRegion !== undefined) {
      writer.uint32(378).string(message.vertexRegion);
    }
    if (message.openaiBaseUrl !== undefined) {
      writer.uint32(386).string(message.openaiBaseUrl);
    }
    if (message.ollamaBaseUrl !== undefined) {
      writer.uint32(394).string(message.ollamaBaseUrl);
    }
    if (message.lmStudioBaseUrl !== undefined) {
      writer.uint32(402).string(message.lmStudioBaseUrl);
    }
    if (message.geminiBaseUrl !== undefined) {
      writer.uint32(410).string(message.geminiBaseUrl);
    }
    if (message.litellmBaseUrl !== undefined) {
      writer.uint32(418).string(message.litellmBaseUrl);
    }
    if (message.asksageApiUrl !== undefined) {
      writer.uint32(426).string(message.asksageApiUrl);
    }
    if (message.litellmApiKey !== undefined) {
      writer.uint32(434).string(message.litellmApiKey);
    }
    if (message.litellmUsePromptCache !== undefined) {
      writer.uint32(440).bool(message.litellmUsePromptCache);
    }
    if (message.thinkingBudgetTokens !== undefined) {
      writer.uint32(448).int64(message.thinkingBudgetTokens);
    }
    if (message.reasoningEffort !== undefined) {
      writer.uint32(458).string(message.reasoningEffort);
    }
    if (message.requestTimeoutMs !== undefined) {
      writer.uint32(464).int64(message.requestTimeoutMs);
    }
    if (message.fireworksModelMaxCompletionTokens !== undefined) {
      writer.uint32(472).int64(message.fireworksModelMaxCompletionTokens);
    }
    if (message.fireworksModelMaxTokens !== undefined) {
      writer.uint32(480).int64(message.fireworksModelMaxTokens);
    }
    if (message.azureApiVersion !== undefined) {
      writer.uint32(490).string(message.azureApiVersion);
    }
    if (message.ollamaApiOptionsCtxNum !== undefined) {
      writer.uint32(498).string(message.ollamaApiOptionsCtxNum);
    }
    if (message.qwenApiLine !== undefined) {
      writer.uint32(506).string(message.qwenApiLine);
    }
    if (message.openrouterProviderSorting !== undefined) {
      writer.uint32(514).string(message.openrouterProviderSorting);
    }
    if (message.vscodeLmModelSelector !== undefined) {
      writer.uint32(522).string(message.vscodeLmModelSelector);
    }
    if (message.openrouterModelInfo !== undefined) {
      writer.uint32(530).string(message.openrouterModelInfo);
    }
    if (message.openaiModelInfo !== undefined) {
      writer.uint32(538).string(message.openaiModelInfo);
    }
    if (message.requestyModelInfo !== undefined) {
      writer.uint32(546).string(message.requestyModelInfo);
    }
    if (message.litellmModelInfo !== undefined) {
      writer.uint32(554).string(message.litellmModelInfo);
    }
    if (message.openaiHeaders !== undefined) {
      writer.uint32(562).string(message.openaiHeaders);
    }
    for (const v of message.favoritedModelIds) {
      writer.uint32(570).string(v!);
    }
    if (message.sapAiCoreClientId !== undefined) {
      writer.uint32(578).string(message.sapAiCoreClientId);
    }
    if (message.sapAiCoreClientSecret !== undefined) {
      writer.uint32(586).string(message.sapAiCoreClientSecret);
    }
    if (message.sapAiCoreBaseUrl !== undefined) {
      writer.uint32(594).string(message.sapAiCoreBaseUrl);
    }
    if (message.sapAiCoreTokenUrl !== undefined) {
      writer.uint32(602).string(message.sapAiCoreTokenUrl);
    }
    if (message.sapAiResourceGroup !== undefined) {
      writer.uint32(610).string(message.sapAiResourceGroup);
    }
    if (message.claudeCodePath !== undefined) {
      writer.uint32(618).string(message.claudeCodePath);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ApiConfiguration {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseApiConfiguration();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.apiProvider = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.apiModelId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.apiKey = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.apiBaseUrl = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.clineApiKey = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.openrouterApiKey = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.anthropicBaseUrl = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.openaiApiKey = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.openaiNativeApiKey = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.geminiApiKey = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.deepseekApiKey = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.requestyApiKey = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.togetherApiKey = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.fireworksApiKey = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.qwenApiKey = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.doubaoApiKey = reader.string();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.mistralApiKey = reader.string();
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.nebiusApiKey = reader.string();
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.asksageApiKey = reader.string();
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.xaiApiKey = reader.string();
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.sambanovaApiKey = reader.string();
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.cerebrasApiKey = reader.string();
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.openrouterModelId = reader.string();
          continue;
        }
        case 24: {
          if (tag !== 194) {
            break;
          }

          message.openaiModelId = reader.string();
          continue;
        }
        case 25: {
          if (tag !== 202) {
            break;
          }

          message.anthropicModelId = reader.string();
          continue;
        }
        case 26: {
          if (tag !== 210) {
            break;
          }

          message.bedrockModelId = reader.string();
          continue;
        }
        case 27: {
          if (tag !== 218) {
            break;
          }

          message.vertexModelId = reader.string();
          continue;
        }
        case 28: {
          if (tag !== 226) {
            break;
          }

          message.geminiModelId = reader.string();
          continue;
        }
        case 29: {
          if (tag !== 234) {
            break;
          }

          message.ollamaModelId = reader.string();
          continue;
        }
        case 30: {
          if (tag !== 242) {
            break;
          }

          message.lmStudioModelId = reader.string();
          continue;
        }
        case 31: {
          if (tag !== 250) {
            break;
          }

          message.litellmModelId = reader.string();
          continue;
        }
        case 32: {
          if (tag !== 258) {
            break;
          }

          message.requestyModelId = reader.string();
          continue;
        }
        case 33: {
          if (tag !== 266) {
            break;
          }

          message.togetherModelId = reader.string();
          continue;
        }
        case 34: {
          if (tag !== 274) {
            break;
          }

          message.fireworksModelId = reader.string();
          continue;
        }
        case 35: {
          if (tag !== 280) {
            break;
          }

          message.awsBedrockCustomSelected = reader.bool();
          continue;
        }
        case 36: {
          if (tag !== 290) {
            break;
          }

          message.awsBedrockCustomModelBaseId = reader.string();
          continue;
        }
        case 37: {
          if (tag !== 298) {
            break;
          }

          message.awsAccessKey = reader.string();
          continue;
        }
        case 38: {
          if (tag !== 306) {
            break;
          }

          message.awsSecretKey = reader.string();
          continue;
        }
        case 39: {
          if (tag !== 314) {
            break;
          }

          message.awsSessionToken = reader.string();
          continue;
        }
        case 40: {
          if (tag !== 322) {
            break;
          }

          message.awsRegion = reader.string();
          continue;
        }
        case 41: {
          if (tag !== 328) {
            break;
          }

          message.awsUseCrossRegionInference = reader.bool();
          continue;
        }
        case 42: {
          if (tag !== 336) {
            break;
          }

          message.awsBedrockUsePromptCache = reader.bool();
          continue;
        }
        case 43: {
          if (tag !== 344) {
            break;
          }

          message.awsUseProfile = reader.bool();
          continue;
        }
        case 44: {
          if (tag !== 354) {
            break;
          }

          message.awsProfile = reader.string();
          continue;
        }
        case 45: {
          if (tag !== 362) {
            break;
          }

          message.awsBedrockEndpoint = reader.string();
          continue;
        }
        case 46: {
          if (tag !== 370) {
            break;
          }

          message.vertexProjectId = reader.string();
          continue;
        }
        case 47: {
          if (tag !== 378) {
            break;
          }

          message.vertexRegion = reader.string();
          continue;
        }
        case 48: {
          if (tag !== 386) {
            break;
          }

          message.openaiBaseUrl = reader.string();
          continue;
        }
        case 49: {
          if (tag !== 394) {
            break;
          }

          message.ollamaBaseUrl = reader.string();
          continue;
        }
        case 50: {
          if (tag !== 402) {
            break;
          }

          message.lmStudioBaseUrl = reader.string();
          continue;
        }
        case 51: {
          if (tag !== 410) {
            break;
          }

          message.geminiBaseUrl = reader.string();
          continue;
        }
        case 52: {
          if (tag !== 418) {
            break;
          }

          message.litellmBaseUrl = reader.string();
          continue;
        }
        case 53: {
          if (tag !== 426) {
            break;
          }

          message.asksageApiUrl = reader.string();
          continue;
        }
        case 54: {
          if (tag !== 434) {
            break;
          }

          message.litellmApiKey = reader.string();
          continue;
        }
        case 55: {
          if (tag !== 440) {
            break;
          }

          message.litellmUsePromptCache = reader.bool();
          continue;
        }
        case 56: {
          if (tag !== 448) {
            break;
          }

          message.thinkingBudgetTokens = longToNumber(reader.int64());
          continue;
        }
        case 57: {
          if (tag !== 458) {
            break;
          }

          message.reasoningEffort = reader.string();
          continue;
        }
        case 58: {
          if (tag !== 464) {
            break;
          }

          message.requestTimeoutMs = longToNumber(reader.int64());
          continue;
        }
        case 59: {
          if (tag !== 472) {
            break;
          }

          message.fireworksModelMaxCompletionTokens = longToNumber(reader.int64());
          continue;
        }
        case 60: {
          if (tag !== 480) {
            break;
          }

          message.fireworksModelMaxTokens = longToNumber(reader.int64());
          continue;
        }
        case 61: {
          if (tag !== 490) {
            break;
          }

          message.azureApiVersion = reader.string();
          continue;
        }
        case 62: {
          if (tag !== 498) {
            break;
          }

          message.ollamaApiOptionsCtxNum = reader.string();
          continue;
        }
        case 63: {
          if (tag !== 506) {
            break;
          }

          message.qwenApiLine = reader.string();
          continue;
        }
        case 64: {
          if (tag !== 514) {
            break;
          }

          message.openrouterProviderSorting = reader.string();
          continue;
        }
        case 65: {
          if (tag !== 522) {
            break;
          }

          message.vscodeLmModelSelector = reader.string();
          continue;
        }
        case 66: {
          if (tag !== 530) {
            break;
          }

          message.openrouterModelInfo = reader.string();
          continue;
        }
        case 67: {
          if (tag !== 538) {
            break;
          }

          message.openaiModelInfo = reader.string();
          continue;
        }
        case 68: {
          if (tag !== 546) {
            break;
          }

          message.requestyModelInfo = reader.string();
          continue;
        }
        case 69: {
          if (tag !== 554) {
            break;
          }

          message.litellmModelInfo = reader.string();
          continue;
        }
        case 70: {
          if (tag !== 562) {
            break;
          }

          message.openaiHeaders = reader.string();
          continue;
        }
        case 71: {
          if (tag !== 570) {
            break;
          }

          message.favoritedModelIds.push(reader.string());
          continue;
        }
        case 72: {
          if (tag !== 578) {
            break;
          }

          message.sapAiCoreClientId = reader.string();
          continue;
        }
        case 73: {
          if (tag !== 586) {
            break;
          }

          message.sapAiCoreClientSecret = reader.string();
          continue;
        }
        case 74: {
          if (tag !== 594) {
            break;
          }

          message.sapAiCoreBaseUrl = reader.string();
          continue;
        }
        case 75: {
          if (tag !== 602) {
            break;
          }

          message.sapAiCoreTokenUrl = reader.string();
          continue;
        }
        case 76: {
          if (tag !== 610) {
            break;
          }

          message.sapAiResourceGroup = reader.string();
          continue;
        }
        case 77: {
          if (tag !== 618) {
            break;
          }

          message.claudeCodePath = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ApiConfiguration {
    return {
      apiProvider: isSet(object.apiProvider) ? globalThis.String(object.apiProvider) : undefined,
      apiModelId: isSet(object.apiModelId) ? globalThis.String(object.apiModelId) : undefined,
      apiKey: isSet(object.apiKey) ? globalThis.String(object.apiKey) : undefined,
      apiBaseUrl: isSet(object.apiBaseUrl) ? globalThis.String(object.apiBaseUrl) : undefined,
      clineApiKey: isSet(object.clineApiKey) ? globalThis.String(object.clineApiKey) : undefined,
      openrouterApiKey: isSet(object.openrouterApiKey) ? globalThis.String(object.openrouterApiKey) : undefined,
      anthropicBaseUrl: isSet(object.anthropicBaseUrl) ? globalThis.String(object.anthropicBaseUrl) : undefined,
      openaiApiKey: isSet(object.openaiApiKey) ? globalThis.String(object.openaiApiKey) : undefined,
      openaiNativeApiKey: isSet(object.openaiNativeApiKey) ? globalThis.String(object.openaiNativeApiKey) : undefined,
      geminiApiKey: isSet(object.geminiApiKey) ? globalThis.String(object.geminiApiKey) : undefined,
      deepseekApiKey: isSet(object.deepseekApiKey) ? globalThis.String(object.deepseekApiKey) : undefined,
      requestyApiKey: isSet(object.requestyApiKey) ? globalThis.String(object.requestyApiKey) : undefined,
      togetherApiKey: isSet(object.togetherApiKey) ? globalThis.String(object.togetherApiKey) : undefined,
      fireworksApiKey: isSet(object.fireworksApiKey) ? globalThis.String(object.fireworksApiKey) : undefined,
      qwenApiKey: isSet(object.qwenApiKey) ? globalThis.String(object.qwenApiKey) : undefined,
      doubaoApiKey: isSet(object.doubaoApiKey) ? globalThis.String(object.doubaoApiKey) : undefined,
      mistralApiKey: isSet(object.mistralApiKey) ? globalThis.String(object.mistralApiKey) : undefined,
      nebiusApiKey: isSet(object.nebiusApiKey) ? globalThis.String(object.nebiusApiKey) : undefined,
      asksageApiKey: isSet(object.asksageApiKey) ? globalThis.String(object.asksageApiKey) : undefined,
      xaiApiKey: isSet(object.xaiApiKey) ? globalThis.String(object.xaiApiKey) : undefined,
      sambanovaApiKey: isSet(object.sambanovaApiKey) ? globalThis.String(object.sambanovaApiKey) : undefined,
      cerebrasApiKey: isSet(object.cerebrasApiKey) ? globalThis.String(object.cerebrasApiKey) : undefined,
      openrouterModelId: isSet(object.openrouterModelId) ? globalThis.String(object.openrouterModelId) : undefined,
      openaiModelId: isSet(object.openaiModelId) ? globalThis.String(object.openaiModelId) : undefined,
      anthropicModelId: isSet(object.anthropicModelId) ? globalThis.String(object.anthropicModelId) : undefined,
      bedrockModelId: isSet(object.bedrockModelId) ? globalThis.String(object.bedrockModelId) : undefined,
      vertexModelId: isSet(object.vertexModelId) ? globalThis.String(object.vertexModelId) : undefined,
      geminiModelId: isSet(object.geminiModelId) ? globalThis.String(object.geminiModelId) : undefined,
      ollamaModelId: isSet(object.ollamaModelId) ? globalThis.String(object.ollamaModelId) : undefined,
      lmStudioModelId: isSet(object.lmStudioModelId) ? globalThis.String(object.lmStudioModelId) : undefined,
      litellmModelId: isSet(object.litellmModelId) ? globalThis.String(object.litellmModelId) : undefined,
      requestyModelId: isSet(object.requestyModelId) ? globalThis.String(object.requestyModelId) : undefined,
      togetherModelId: isSet(object.togetherModelId) ? globalThis.String(object.togetherModelId) : undefined,
      fireworksModelId: isSet(object.fireworksModelId) ? globalThis.String(object.fireworksModelId) : undefined,
      awsBedrockCustomSelected: isSet(object.awsBedrockCustomSelected)
        ? globalThis.Boolean(object.awsBedrockCustomSelected)
        : undefined,
      awsBedrockCustomModelBaseId: isSet(object.awsBedrockCustomModelBaseId)
        ? globalThis.String(object.awsBedrockCustomModelBaseId)
        : undefined,
      awsAccessKey: isSet(object.awsAccessKey) ? globalThis.String(object.awsAccessKey) : undefined,
      awsSecretKey: isSet(object.awsSecretKey) ? globalThis.String(object.awsSecretKey) : undefined,
      awsSessionToken: isSet(object.awsSessionToken) ? globalThis.String(object.awsSessionToken) : undefined,
      awsRegion: isSet(object.awsRegion) ? globalThis.String(object.awsRegion) : undefined,
      awsUseCrossRegionInference: isSet(object.awsUseCrossRegionInference)
        ? globalThis.Boolean(object.awsUseCrossRegionInference)
        : undefined,
      awsBedrockUsePromptCache: isSet(object.awsBedrockUsePromptCache)
        ? globalThis.Boolean(object.awsBedrockUsePromptCache)
        : undefined,
      awsUseProfile: isSet(object.awsUseProfile) ? globalThis.Boolean(object.awsUseProfile) : undefined,
      awsProfile: isSet(object.awsProfile) ? globalThis.String(object.awsProfile) : undefined,
      awsBedrockEndpoint: isSet(object.awsBedrockEndpoint) ? globalThis.String(object.awsBedrockEndpoint) : undefined,
      vertexProjectId: isSet(object.vertexProjectId) ? globalThis.String(object.vertexProjectId) : undefined,
      vertexRegion: isSet(object.vertexRegion) ? globalThis.String(object.vertexRegion) : undefined,
      openaiBaseUrl: isSet(object.openaiBaseUrl) ? globalThis.String(object.openaiBaseUrl) : undefined,
      ollamaBaseUrl: isSet(object.ollamaBaseUrl) ? globalThis.String(object.ollamaBaseUrl) : undefined,
      lmStudioBaseUrl: isSet(object.lmStudioBaseUrl) ? globalThis.String(object.lmStudioBaseUrl) : undefined,
      geminiBaseUrl: isSet(object.geminiBaseUrl) ? globalThis.String(object.geminiBaseUrl) : undefined,
      litellmBaseUrl: isSet(object.litellmBaseUrl) ? globalThis.String(object.litellmBaseUrl) : undefined,
      asksageApiUrl: isSet(object.asksageApiUrl) ? globalThis.String(object.asksageApiUrl) : undefined,
      litellmApiKey: isSet(object.litellmApiKey) ? globalThis.String(object.litellmApiKey) : undefined,
      litellmUsePromptCache: isSet(object.litellmUsePromptCache)
        ? globalThis.Boolean(object.litellmUsePromptCache)
        : undefined,
      thinkingBudgetTokens: isSet(object.thinkingBudgetTokens)
        ? globalThis.Number(object.thinkingBudgetTokens)
        : undefined,
      reasoningEffort: isSet(object.reasoningEffort) ? globalThis.String(object.reasoningEffort) : undefined,
      requestTimeoutMs: isSet(object.requestTimeoutMs) ? globalThis.Number(object.requestTimeoutMs) : undefined,
      fireworksModelMaxCompletionTokens: isSet(object.fireworksModelMaxCompletionTokens)
        ? globalThis.Number(object.fireworksModelMaxCompletionTokens)
        : undefined,
      fireworksModelMaxTokens: isSet(object.fireworksModelMaxTokens)
        ? globalThis.Number(object.fireworksModelMaxTokens)
        : undefined,
      azureApiVersion: isSet(object.azureApiVersion) ? globalThis.String(object.azureApiVersion) : undefined,
      ollamaApiOptionsCtxNum: isSet(object.ollamaApiOptionsCtxNum)
        ? globalThis.String(object.ollamaApiOptionsCtxNum)
        : undefined,
      qwenApiLine: isSet(object.qwenApiLine) ? globalThis.String(object.qwenApiLine) : undefined,
      openrouterProviderSorting: isSet(object.openrouterProviderSorting)
        ? globalThis.String(object.openrouterProviderSorting)
        : undefined,
      vscodeLmModelSelector: isSet(object.vscodeLmModelSelector)
        ? globalThis.String(object.vscodeLmModelSelector)
        : undefined,
      openrouterModelInfo: isSet(object.openrouterModelInfo)
        ? globalThis.String(object.openrouterModelInfo)
        : undefined,
      openaiModelInfo: isSet(object.openaiModelInfo) ? globalThis.String(object.openaiModelInfo) : undefined,
      requestyModelInfo: isSet(object.requestyModelInfo) ? globalThis.String(object.requestyModelInfo) : undefined,
      litellmModelInfo: isSet(object.litellmModelInfo) ? globalThis.String(object.litellmModelInfo) : undefined,
      openaiHeaders: isSet(object.openaiHeaders) ? globalThis.String(object.openaiHeaders) : undefined,
      favoritedModelIds: globalThis.Array.isArray(object?.favoritedModelIds)
        ? object.favoritedModelIds.map((e: any) => globalThis.String(e))
        : [],
      sapAiCoreClientId: isSet(object.sapAiCoreClientId) ? globalThis.String(object.sapAiCoreClientId) : undefined,
      sapAiCoreClientSecret: isSet(object.sapAiCoreClientSecret)
        ? globalThis.String(object.sapAiCoreClientSecret)
        : undefined,
      sapAiCoreBaseUrl: isSet(object.sapAiCoreBaseUrl) ? globalThis.String(object.sapAiCoreBaseUrl) : undefined,
      sapAiCoreTokenUrl: isSet(object.sapAiCoreTokenUrl) ? globalThis.String(object.sapAiCoreTokenUrl) : undefined,
      sapAiResourceGroup: isSet(object.sapAiResourceGroup) ? globalThis.String(object.sapAiResourceGroup) : undefined,
      claudeCodePath: isSet(object.claudeCodePath) ? globalThis.String(object.claudeCodePath) : undefined,
    };
  },

  toJSON(message: ApiConfiguration): unknown {
    const obj: any = {};
    if (message.apiProvider !== undefined) {
      obj.apiProvider = message.apiProvider;
    }
    if (message.apiModelId !== undefined) {
      obj.apiModelId = message.apiModelId;
    }
    if (message.apiKey !== undefined) {
      obj.apiKey = message.apiKey;
    }
    if (message.apiBaseUrl !== undefined) {
      obj.apiBaseUrl = message.apiBaseUrl;
    }
    if (message.clineApiKey !== undefined) {
      obj.clineApiKey = message.clineApiKey;
    }
    if (message.openrouterApiKey !== undefined) {
      obj.openrouterApiKey = message.openrouterApiKey;
    }
    if (message.anthropicBaseUrl !== undefined) {
      obj.anthropicBaseUrl = message.anthropicBaseUrl;
    }
    if (message.openaiApiKey !== undefined) {
      obj.openaiApiKey = message.openaiApiKey;
    }
    if (message.openaiNativeApiKey !== undefined) {
      obj.openaiNativeApiKey = message.openaiNativeApiKey;
    }
    if (message.geminiApiKey !== undefined) {
      obj.geminiApiKey = message.geminiApiKey;
    }
    if (message.deepseekApiKey !== undefined) {
      obj.deepseekApiKey = message.deepseekApiKey;
    }
    if (message.requestyApiKey !== undefined) {
      obj.requestyApiKey = message.requestyApiKey;
    }
    if (message.togetherApiKey !== undefined) {
      obj.togetherApiKey = message.togetherApiKey;
    }
    if (message.fireworksApiKey !== undefined) {
      obj.fireworksApiKey = message.fireworksApiKey;
    }
    if (message.qwenApiKey !== undefined) {
      obj.qwenApiKey = message.qwenApiKey;
    }
    if (message.doubaoApiKey !== undefined) {
      obj.doubaoApiKey = message.doubaoApiKey;
    }
    if (message.mistralApiKey !== undefined) {
      obj.mistralApiKey = message.mistralApiKey;
    }
    if (message.nebiusApiKey !== undefined) {
      obj.nebiusApiKey = message.nebiusApiKey;
    }
    if (message.asksageApiKey !== undefined) {
      obj.asksageApiKey = message.asksageApiKey;
    }
    if (message.xaiApiKey !== undefined) {
      obj.xaiApiKey = message.xaiApiKey;
    }
    if (message.sambanovaApiKey !== undefined) {
      obj.sambanovaApiKey = message.sambanovaApiKey;
    }
    if (message.cerebrasApiKey !== undefined) {
      obj.cerebrasApiKey = message.cerebrasApiKey;
    }
    if (message.openrouterModelId !== undefined) {
      obj.openrouterModelId = message.openrouterModelId;
    }
    if (message.openaiModelId !== undefined) {
      obj.openaiModelId = message.openaiModelId;
    }
    if (message.anthropicModelId !== undefined) {
      obj.anthropicModelId = message.anthropicModelId;
    }
    if (message.bedrockModelId !== undefined) {
      obj.bedrockModelId = message.bedrockModelId;
    }
    if (message.vertexModelId !== undefined) {
      obj.vertexModelId = message.vertexModelId;
    }
    if (message.geminiModelId !== undefined) {
      obj.geminiModelId = message.geminiModelId;
    }
    if (message.ollamaModelId !== undefined) {
      obj.ollamaModelId = message.ollamaModelId;
    }
    if (message.lmStudioModelId !== undefined) {
      obj.lmStudioModelId = message.lmStudioModelId;
    }
    if (message.litellmModelId !== undefined) {
      obj.litellmModelId = message.litellmModelId;
    }
    if (message.requestyModelId !== undefined) {
      obj.requestyModelId = message.requestyModelId;
    }
    if (message.togetherModelId !== undefined) {
      obj.togetherModelId = message.togetherModelId;
    }
    if (message.fireworksModelId !== undefined) {
      obj.fireworksModelId = message.fireworksModelId;
    }
    if (message.awsBedrockCustomSelected !== undefined) {
      obj.awsBedrockCustomSelected = message.awsBedrockCustomSelected;
    }
    if (message.awsBedrockCustomModelBaseId !== undefined) {
      obj.awsBedrockCustomModelBaseId = message.awsBedrockCustomModelBaseId;
    }
    if (message.awsAccessKey !== undefined) {
      obj.awsAccessKey = message.awsAccessKey;
    }
    if (message.awsSecretKey !== undefined) {
      obj.awsSecretKey = message.awsSecretKey;
    }
    if (message.awsSessionToken !== undefined) {
      obj.awsSessionToken = message.awsSessionToken;
    }
    if (message.awsRegion !== undefined) {
      obj.awsRegion = message.awsRegion;
    }
    if (message.awsUseCrossRegionInference !== undefined) {
      obj.awsUseCrossRegionInference = message.awsUseCrossRegionInference;
    }
    if (message.awsBedrockUsePromptCache !== undefined) {
      obj.awsBedrockUsePromptCache = message.awsBedrockUsePromptCache;
    }
    if (message.awsUseProfile !== undefined) {
      obj.awsUseProfile = message.awsUseProfile;
    }
    if (message.awsProfile !== undefined) {
      obj.awsProfile = message.awsProfile;
    }
    if (message.awsBedrockEndpoint !== undefined) {
      obj.awsBedrockEndpoint = message.awsBedrockEndpoint;
    }
    if (message.vertexProjectId !== undefined) {
      obj.vertexProjectId = message.vertexProjectId;
    }
    if (message.vertexRegion !== undefined) {
      obj.vertexRegion = message.vertexRegion;
    }
    if (message.openaiBaseUrl !== undefined) {
      obj.openaiBaseUrl = message.openaiBaseUrl;
    }
    if (message.ollamaBaseUrl !== undefined) {
      obj.ollamaBaseUrl = message.ollamaBaseUrl;
    }
    if (message.lmStudioBaseUrl !== undefined) {
      obj.lmStudioBaseUrl = message.lmStudioBaseUrl;
    }
    if (message.geminiBaseUrl !== undefined) {
      obj.geminiBaseUrl = message.geminiBaseUrl;
    }
    if (message.litellmBaseUrl !== undefined) {
      obj.litellmBaseUrl = message.litellmBaseUrl;
    }
    if (message.asksageApiUrl !== undefined) {
      obj.asksageApiUrl = message.asksageApiUrl;
    }
    if (message.litellmApiKey !== undefined) {
      obj.litellmApiKey = message.litellmApiKey;
    }
    if (message.litellmUsePromptCache !== undefined) {
      obj.litellmUsePromptCache = message.litellmUsePromptCache;
    }
    if (message.thinkingBudgetTokens !== undefined) {
      obj.thinkingBudgetTokens = Math.round(message.thinkingBudgetTokens);
    }
    if (message.reasoningEffort !== undefined) {
      obj.reasoningEffort = message.reasoningEffort;
    }
    if (message.requestTimeoutMs !== undefined) {
      obj.requestTimeoutMs = Math.round(message.requestTimeoutMs);
    }
    if (message.fireworksModelMaxCompletionTokens !== undefined) {
      obj.fireworksModelMaxCompletionTokens = Math.round(message.fireworksModelMaxCompletionTokens);
    }
    if (message.fireworksModelMaxTokens !== undefined) {
      obj.fireworksModelMaxTokens = Math.round(message.fireworksModelMaxTokens);
    }
    if (message.azureApiVersion !== undefined) {
      obj.azureApiVersion = message.azureApiVersion;
    }
    if (message.ollamaApiOptionsCtxNum !== undefined) {
      obj.ollamaApiOptionsCtxNum = message.ollamaApiOptionsCtxNum;
    }
    if (message.qwenApiLine !== undefined) {
      obj.qwenApiLine = message.qwenApiLine;
    }
    if (message.openrouterProviderSorting !== undefined) {
      obj.openrouterProviderSorting = message.openrouterProviderSorting;
    }
    if (message.vscodeLmModelSelector !== undefined) {
      obj.vscodeLmModelSelector = message.vscodeLmModelSelector;
    }
    if (message.openrouterModelInfo !== undefined) {
      obj.openrouterModelInfo = message.openrouterModelInfo;
    }
    if (message.openaiModelInfo !== undefined) {
      obj.openaiModelInfo = message.openaiModelInfo;
    }
    if (message.requestyModelInfo !== undefined) {
      obj.requestyModelInfo = message.requestyModelInfo;
    }
    if (message.litellmModelInfo !== undefined) {
      obj.litellmModelInfo = message.litellmModelInfo;
    }
    if (message.openaiHeaders !== undefined) {
      obj.openaiHeaders = message.openaiHeaders;
    }
    if (message.favoritedModelIds?.length) {
      obj.favoritedModelIds = message.favoritedModelIds;
    }
    if (message.sapAiCoreClientId !== undefined) {
      obj.sapAiCoreClientId = message.sapAiCoreClientId;
    }
    if (message.sapAiCoreClientSecret !== undefined) {
      obj.sapAiCoreClientSecret = message.sapAiCoreClientSecret;
    }
    if (message.sapAiCoreBaseUrl !== undefined) {
      obj.sapAiCoreBaseUrl = message.sapAiCoreBaseUrl;
    }
    if (message.sapAiCoreTokenUrl !== undefined) {
      obj.sapAiCoreTokenUrl = message.sapAiCoreTokenUrl;
    }
    if (message.sapAiResourceGroup !== undefined) {
      obj.sapAiResourceGroup = message.sapAiResourceGroup;
    }
    if (message.claudeCodePath !== undefined) {
      obj.claudeCodePath = message.claudeCodePath;
    }
    return obj;
  },

  create(base?: DeepPartial<ApiConfiguration>): ApiConfiguration {
    return ApiConfiguration.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ApiConfiguration>): ApiConfiguration {
    const message = createBaseApiConfiguration();
    message.apiProvider = object.apiProvider ?? undefined;
    message.apiModelId = object.apiModelId ?? undefined;
    message.apiKey = object.apiKey ?? undefined;
    message.apiBaseUrl = object.apiBaseUrl ?? undefined;
    message.clineApiKey = object.clineApiKey ?? undefined;
    message.openrouterApiKey = object.openrouterApiKey ?? undefined;
    message.anthropicBaseUrl = object.anthropicBaseUrl ?? undefined;
    message.openaiApiKey = object.openaiApiKey ?? undefined;
    message.openaiNativeApiKey = object.openaiNativeApiKey ?? undefined;
    message.geminiApiKey = object.geminiApiKey ?? undefined;
    message.deepseekApiKey = object.deepseekApiKey ?? undefined;
    message.requestyApiKey = object.requestyApiKey ?? undefined;
    message.togetherApiKey = object.togetherApiKey ?? undefined;
    message.fireworksApiKey = object.fireworksApiKey ?? undefined;
    message.qwenApiKey = object.qwenApiKey ?? undefined;
    message.doubaoApiKey = object.doubaoApiKey ?? undefined;
    message.mistralApiKey = object.mistralApiKey ?? undefined;
    message.nebiusApiKey = object.nebiusApiKey ?? undefined;
    message.asksageApiKey = object.asksageApiKey ?? undefined;
    message.xaiApiKey = object.xaiApiKey ?? undefined;
    message.sambanovaApiKey = object.sambanovaApiKey ?? undefined;
    message.cerebrasApiKey = object.cerebrasApiKey ?? undefined;
    message.openrouterModelId = object.openrouterModelId ?? undefined;
    message.openaiModelId = object.openaiModelId ?? undefined;
    message.anthropicModelId = object.anthropicModelId ?? undefined;
    message.bedrockModelId = object.bedrockModelId ?? undefined;
    message.vertexModelId = object.vertexModelId ?? undefined;
    message.geminiModelId = object.geminiModelId ?? undefined;
    message.ollamaModelId = object.ollamaModelId ?? undefined;
    message.lmStudioModelId = object.lmStudioModelId ?? undefined;
    message.litellmModelId = object.litellmModelId ?? undefined;
    message.requestyModelId = object.requestyModelId ?? undefined;
    message.togetherModelId = object.togetherModelId ?? undefined;
    message.fireworksModelId = object.fireworksModelId ?? undefined;
    message.awsBedrockCustomSelected = object.awsBedrockCustomSelected ?? undefined;
    message.awsBedrockCustomModelBaseId = object.awsBedrockCustomModelBaseId ?? undefined;
    message.awsAccessKey = object.awsAccessKey ?? undefined;
    message.awsSecretKey = object.awsSecretKey ?? undefined;
    message.awsSessionToken = object.awsSessionToken ?? undefined;
    message.awsRegion = object.awsRegion ?? undefined;
    message.awsUseCrossRegionInference = object.awsUseCrossRegionInference ?? undefined;
    message.awsBedrockUsePromptCache = object.awsBedrockUsePromptCache ?? undefined;
    message.awsUseProfile = object.awsUseProfile ?? undefined;
    message.awsProfile = object.awsProfile ?? undefined;
    message.awsBedrockEndpoint = object.awsBedrockEndpoint ?? undefined;
    message.vertexProjectId = object.vertexProjectId ?? undefined;
    message.vertexRegion = object.vertexRegion ?? undefined;
    message.openaiBaseUrl = object.openaiBaseUrl ?? undefined;
    message.ollamaBaseUrl = object.ollamaBaseUrl ?? undefined;
    message.lmStudioBaseUrl = object.lmStudioBaseUrl ?? undefined;
    message.geminiBaseUrl = object.geminiBaseUrl ?? undefined;
    message.litellmBaseUrl = object.litellmBaseUrl ?? undefined;
    message.asksageApiUrl = object.asksageApiUrl ?? undefined;
    message.litellmApiKey = object.litellmApiKey ?? undefined;
    message.litellmUsePromptCache = object.litellmUsePromptCache ?? undefined;
    message.thinkingBudgetTokens = object.thinkingBudgetTokens ?? undefined;
    message.reasoningEffort = object.reasoningEffort ?? undefined;
    message.requestTimeoutMs = object.requestTimeoutMs ?? undefined;
    message.fireworksModelMaxCompletionTokens = object.fireworksModelMaxCompletionTokens ?? undefined;
    message.fireworksModelMaxTokens = object.fireworksModelMaxTokens ?? undefined;
    message.azureApiVersion = object.azureApiVersion ?? undefined;
    message.ollamaApiOptionsCtxNum = object.ollamaApiOptionsCtxNum ?? undefined;
    message.qwenApiLine = object.qwenApiLine ?? undefined;
    message.openrouterProviderSorting = object.openrouterProviderSorting ?? undefined;
    message.vscodeLmModelSelector = object.vscodeLmModelSelector ?? undefined;
    message.openrouterModelInfo = object.openrouterModelInfo ?? undefined;
    message.openaiModelInfo = object.openaiModelInfo ?? undefined;
    message.requestyModelInfo = object.requestyModelInfo ?? undefined;
    message.litellmModelInfo = object.litellmModelInfo ?? undefined;
    message.openaiHeaders = object.openaiHeaders ?? undefined;
    message.favoritedModelIds = object.favoritedModelIds?.map((e) => e) || [];
    message.sapAiCoreClientId = object.sapAiCoreClientId ?? undefined;
    message.sapAiCoreClientSecret = object.sapAiCoreClientSecret ?? undefined;
    message.sapAiCoreBaseUrl = object.sapAiCoreBaseUrl ?? undefined;
    message.sapAiCoreTokenUrl = object.sapAiCoreTokenUrl ?? undefined;
    message.sapAiResourceGroup = object.sapAiResourceGroup ?? undefined;
    message.claudeCodePath = object.claudeCodePath ?? undefined;
    return message;
  },
};

export type StateServiceDefinition = typeof StateServiceDefinition;
export const StateServiceDefinition = {
  name: "StateService",
  fullName: "cline.StateService",
  methods: {
    getLatestState: {
      name: "getLatestState",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: State,
      responseStream: false,
      options: {},
    },
    updateTerminalConnectionTimeout: {
      name: "updateTerminalConnectionTimeout",
      requestType: Int64Request,
      requestStream: false,
      responseType: Int64,
      responseStream: false,
      options: {},
    },
    updateTerminalReuseEnabled: {
      name: "updateTerminalReuseEnabled",
      requestType: BooleanRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {},
    },
    updateDefaultTerminalProfile: {
      name: "updateDefaultTerminalProfile",
      requestType: StringRequest,
      requestStream: false,
      responseType: TerminalProfileUpdateResponse,
      responseStream: false,
      options: {},
    },
    getAvailableTerminalProfiles: {
      name: "getAvailableTerminalProfiles",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: TerminalProfiles,
      responseStream: false,
      options: {},
    },
    subscribeToState: {
      name: "subscribeToState",
      requestType: EmptyRequest,
      requestStream: false,
      responseType: State,
      responseStream: true,
      options: {},
    },
    toggleFavoriteModel: {
      name: "toggleFavoriteModel",
      requestType: StringRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {},
    },
    resetState: {
      name: "resetState",
      requestType: ResetStateRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {},
    },
    togglePlanActMode: {
      name: "togglePlanActMode",
      requestType: TogglePlanActModeRequest,
      requestStream: false,
      responseType: Boolean,
      responseStream: false,
      options: {},
    },
    updateAutoApprovalSettings: {
      name: "updateAutoApprovalSettings",
      requestType: AutoApprovalSettingsRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {},
    },
    updateSettings: {
      name: "updateSettings",
      requestType: UpdateSettingsRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {},
    },
  },
} as const;

export interface StateServiceImplementation<CallContextExt = {}> {
  getLatestState(request: EmptyRequest, context: CallContext & CallContextExt): Promise<DeepPartial<State>>;
  updateTerminalConnectionTimeout(
    request: Int64Request,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<Int64>>;
  updateTerminalReuseEnabled(
    request: BooleanRequest,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<Empty>>;
  updateDefaultTerminalProfile(
    request: StringRequest,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<TerminalProfileUpdateResponse>>;
  getAvailableTerminalProfiles(
    request: EmptyRequest,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<TerminalProfiles>>;
  subscribeToState(
    request: EmptyRequest,
    context: CallContext & CallContextExt,
  ): ServerStreamingMethodResult<DeepPartial<State>>;
  toggleFavoriteModel(request: StringRequest, context: CallContext & CallContextExt): Promise<DeepPartial<Empty>>;
  resetState(request: ResetStateRequest, context: CallContext & CallContextExt): Promise<DeepPartial<Empty>>;
  togglePlanActMode(
    request: TogglePlanActModeRequest,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<Boolean>>;
  updateAutoApprovalSettings(
    request: AutoApprovalSettingsRequest,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<Empty>>;
  updateSettings(request: UpdateSettingsRequest, context: CallContext & CallContextExt): Promise<DeepPartial<Empty>>;
}

export interface StateServiceClient<CallOptionsExt = {}> {
  getLatestState(request: DeepPartial<EmptyRequest>, options?: CallOptions & CallOptionsExt): Promise<State>;
  updateTerminalConnectionTimeout(
    request: DeepPartial<Int64Request>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<Int64>;
  updateTerminalReuseEnabled(
    request: DeepPartial<BooleanRequest>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<Empty>;
  updateDefaultTerminalProfile(
    request: DeepPartial<StringRequest>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<TerminalProfileUpdateResponse>;
  getAvailableTerminalProfiles(
    request: DeepPartial<EmptyRequest>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<TerminalProfiles>;
  subscribeToState(request: DeepPartial<EmptyRequest>, options?: CallOptions & CallOptionsExt): AsyncIterable<State>;
  toggleFavoriteModel(request: DeepPartial<StringRequest>, options?: CallOptions & CallOptionsExt): Promise<Empty>;
  resetState(request: DeepPartial<ResetStateRequest>, options?: CallOptions & CallOptionsExt): Promise<Empty>;
  togglePlanActMode(
    request: DeepPartial<TogglePlanActModeRequest>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<Boolean>;
  updateAutoApprovalSettings(
    request: DeepPartial<AutoApprovalSettingsRequest>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<Empty>;
  updateSettings(request: DeepPartial<UpdateSettingsRequest>, options?: CallOptions & CallOptionsExt): Promise<Empty>;
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

type ServerStreamingMethodResult<Response> = { [Symbol.asyncIterator](): AsyncIterator<Response, void> };

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
