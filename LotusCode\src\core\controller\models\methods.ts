// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js

// Import all method implementations
import { registerMethod } from "./index"
import { getLmStudioModels } from "./getLmStudioModels"
import { getOllamaModels } from "./getOllamaModels"
import { getVsCodeLmModels } from "./getVsCodeLmModels"
import { refreshOpenAiModels } from "./refreshOpenAiModels"
import { refreshOpenRouterModels } from "./refreshOpenRouterModels"
import { refreshRequestyModels } from "./refreshRequestyModels"
import { subscribeToOpenRouterModels } from "./subscribeToOpenRouterModels"
import { updateApiConfigurationProto } from "./updateApiConfigurationProto"

// Streaming methods for this service
export const streamingMethods = [
  "subscribeToOpenRouterModels"
]

// Register all models service methods
export function registerAllMethods(): void {
	// Register each method with the registry
	registerMethod("getLmStudioModels", getLmStudioModels)
	registerMethod("getOllamaModels", getOllamaModels)
	registerMethod("getVsCodeLmModels", getVsCodeLmModels)
	registerMethod("refreshOpenAiModels", refreshOpenAiModels)
	registerMethod("refreshOpenRouterModels", refreshOpenRouterModels)
	registerMethod("refreshRequestyModels", refreshRequestyModels)
	registerMethod("subscribeToOpenRouterModels", subscribeToOpenRouterModels, { isStreaming: true })
	registerMethod("updateApiConfigurationProto", updateApiConfigurationProto)
}