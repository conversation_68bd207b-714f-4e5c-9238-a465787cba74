// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js

import { createServiceRegistry, ServiceMethodHandler, StreamingMethodHandler } from "../grpc-service"
import { StreamingResponseHandler } from "../grpc-handler"
import { registerAllMethods } from "./methods"

// Create task service registry
const taskService = createServiceRegistry("task")

// Export the method handler types and registration function
export type TaskMethodHandler = ServiceMethodHandler
export type TaskStreamingMethodHandler = StreamingMethodHandler
export const registerMethod = taskService.registerMethod

// Export the request handlers
export const handleTaskServiceRequest = taskService.handleRequest
export const handleTaskServiceStreamingRequest = taskService.handleStreamingRequest
export const isStreamingMethod = taskService.isStreamingMethod

// Register all task methods
registerAllMethods()