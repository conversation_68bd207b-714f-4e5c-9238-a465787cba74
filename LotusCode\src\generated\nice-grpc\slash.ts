// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: slash.proto

/* eslint-disable */
import { type CallContext, type CallOptions } from "nice-grpc-common";
import { Empty, StringRequest } from "./common";

/** SlashService provides methods for managing slash */
export type SlashServiceDefinition = typeof SlashServiceDefinition;
export const SlashServiceDefinition = {
  name: "SlashService",
  fullName: "cline.SlashService",
  methods: {
    /** Sends button click message */
    reportBug: {
      name: "reportBug",
      requestType: StringRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {},
    },
    condense: {
      name: "condense",
      requestType: StringRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {},
    },
  },
} as const;

export interface SlashServiceImplementation<CallContextExt = {}> {
  /** Sends button click message */
  reportBug(request: StringRequest, context: CallContext & CallContextExt): Promise<DeepPartial<Empty>>;
  condense(request: StringRequest, context: CallContext & CallContextExt): Promise<DeepPartial<Empty>>;
}

export interface SlashServiceClient<CallOptionsExt = {}> {
  /** Sends button click message */
  reportBug(request: DeepPartial<StringRequest>, options?: CallOptions & CallOptionsExt): Promise<Empty>;
  condense(request: DeepPartial<StringRequest>, options?: CallOptions & CallOptionsExt): Promise<Empty>;
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;
