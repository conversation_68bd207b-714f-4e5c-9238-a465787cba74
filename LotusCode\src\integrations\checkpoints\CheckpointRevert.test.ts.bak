import { expect } from "chai"
import { describe, it, beforeEach, afterEach } from "mocha"
import fs from "fs/promises"
import path from "path"
import { createTestEnvironment, createTestTracker } from "./Checkpoint-test-utils"

describe("Checkpoint Revert Operations", () => {
    let env: Awaited<ReturnType<typeof createTestEnvironment>>

    beforeEach(async () => {
        env = await createTestEnvironment()
    })

    afterEach(async () => {
        await env.cleanup()
    })

    it("should revert working directory to a previous checkpoint state", async () => {
        const tracker = await createTestTracker(env.globalStoragePath)
        if (!tracker) {throw new Error("Failed to create tracker")}

        // Create and commit initial state
        await fs.writeFile(env.testFilePath, "initial content")
        const firstCommit = await tracker.commit()
        expect(firstCommit).to.not.be.undefined

        // Create and commit changes
        await fs.writeFile(env.testFilePath, "modified content")
        const secondCommit = await tracker.commit()
        expect(secondCommit).to.not.be.undefined

        // Make more changes without committing
        await fs.writeFile(env.testFilePath, "uncommitted changes")

        // Revert to first commit
        await tracker.resetHead(firstCommit!)

        // Verify file content matches initial state
        const resetContent = await fs.readFile(env.testFilePath, "utf8")
        expect(resetContent).to.equal("initial content")
    })

    it("should handle reverting with multiple files", async () => {
        const tracker = await createTestTracker(env.globalStoragePath)
        if (!tracker) {throw new Error("Failed to create tracker")}

        // Create and commit initial state with multiple files
        const testFile2Path = path.join(env.tempDir, "src", "test2.txt")
        await fs.writeFile(env.testFilePath, "file1 initial")
        await fs.writeFile(testFile2Path, "file2 initial")
        const firstCommit = await tracker.commit()
        expect(firstCommit).to.not.be.undefined

        // Modify both files and commit
        await fs.writeFile(env.testFilePath, "file1 modified")
        await fs.writeFile(testFile2Path, "file2 modified")
        const secondCommit = await tracker.commit()
        expect(secondCommit).to.not.be.undefined

        // Make more changes
        await fs.writeFile(env.testFilePath, "file1 uncommitted")
        await fs.writeFile(testFile2Path, "file2 uncommitted")

        // Reset to first commit
        await tracker.resetHead(firstCommit!)

        // Verify both files match initial state
        const file1Content = await fs.readFile(env.testFilePath, "utf8")
        const file2Content = await fs.readFile(testFile2Path, "utf8")
        expect(file1Content).to.equal("file1 initial")
        expect(file2Content).to.equal("file2 initial")
    })

    it("should handle reverting when files are deleted", async () => {
        const tracker = await createTestTracker(env.globalStoragePath)
        if (!tracker) {throw new Error("Failed to create tracker")}

        // Create and commit initial state
        await fs.writeFile(env.testFilePath, "initial content")
        const firstCommit = await tracker.commit()
        expect(firstCommit).to.not.be.undefined

        // Delete file and commit
        await fs.unlink(env.testFilePath)
        const secondCommit = await tracker.commit()
        expect(secondCommit).to.not.be.undefined

        // Revert to first commit
        await tracker.resetHead(firstCommit!)

        // Verify file is restored with original content
        const resetContent = await fs.readFile(env.testFilePath, "utf8")
        expect(resetContent).to.equal("initial content")
    })
})
