// GENERATED CODE -- DO NOT EDIT!
// Generated by scripts/generate-host-bridge-client.mjs
import * as proto from "@shared/proto/index"
import { StreamingCallbacks } from "@hosts/host-provider-types"

/**
 * Interface for UriService client.
 */
export interface UriServiceClientInterface {

	file(request: proto.cline.StringRequest): Promise<proto.host.Uri>;

	joinPath(request: proto.host.JoinPathRequest): Promise<proto.host.Uri>;

	parse(request: proto.cline.StringRequest): Promise<proto.host.Uri>;
}

/**
 * Interface for WatchService client.
 */
export interface WatchServiceClientInterface {

	subscribeToFile(request: proto.host.SubscribeToFileRequest, callbacks: StreamingCallbacks<proto.host.FileChangeEvent>): () => void;
}
