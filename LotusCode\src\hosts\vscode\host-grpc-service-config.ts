// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js

import { StreamingResponseHandler } from "./host-grpc-handler"
import { handleUriServiceRequest, handleUriServiceStreamingRequest } from "./uri/index"
import { handleWatchServiceRequest, handleWatchServiceStreamingRequest } from "./watch/index"

/**
 * Configuration for a host service handler
 */
export interface HostServiceHandlerConfig {
  requestHandler: (method: string, message: any) => Promise<any>;
  streamingHandler: (method: string, message: any, responseStream: StreamingResponseHandler, requestId?: string) => Promise<void>;
}

/**
 * Map of host service names to their handler configurations
 */
export const hostServiceHandlers: Record<string, HostServiceHandlerConfig> = {
  "host.UriService": {
    requestHandler: handleUriServiceRequest,
    streamingHandler: handleUriServiceStreamingRequest
  },
  "host.WatchService": {
    requestHandler: handleWatchServiceRequest,
    streamingHandler: handleWatchServiceStreamingRequest
  }
};