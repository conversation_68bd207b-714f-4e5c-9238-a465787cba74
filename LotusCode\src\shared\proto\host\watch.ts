// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: host/watch.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Metadata } from "../common";

/** Request to subscribe to file changes */
export interface SubscribeToFileRequest {
  metadata?: Metadata | undefined;
  path: string;
}

/** Event representing a file change */
export interface FileChangeEvent {
  path: string;
  type: FileChangeEvent_ChangeType;
  /** Optional content of the file after change */
  content: string;
}

export enum FileChangeEvent_ChangeType {
  CREATED = 0,
  CHANGED = 1,
  DELETED = 2,
  UNRECOGNIZED = -1,
}

export function fileChangeEvent_ChangeTypeFromJSON(object: any): FileChangeEvent_ChangeType {
  switch (object) {
    case 0:
    case "CREATED":
      return FileChangeEvent_ChangeType.CREATED;
    case 1:
    case "CHANGED":
      return FileChangeEvent_ChangeType.CHANGED;
    case 2:
    case "DELETED":
      return FileChangeEvent_ChangeType.DELETED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return FileChangeEvent_ChangeType.UNRECOGNIZED;
  }
}

export function fileChangeEvent_ChangeTypeToJSON(object: FileChangeEvent_ChangeType): string {
  switch (object) {
    case FileChangeEvent_ChangeType.CREATED:
      return "CREATED";
    case FileChangeEvent_ChangeType.CHANGED:
      return "CHANGED";
    case FileChangeEvent_ChangeType.DELETED:
      return "DELETED";
    case FileChangeEvent_ChangeType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

function createBaseSubscribeToFileRequest(): SubscribeToFileRequest {
  return { metadata: undefined, path: "" };
}

export const SubscribeToFileRequest: MessageFns<SubscribeToFileRequest> = {
  encode(message: SubscribeToFileRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(10).fork()).join();
    }
    if (message.path !== "") {
      writer.uint32(18).string(message.path);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeToFileRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeToFileRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.metadata = Metadata.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.path = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubscribeToFileRequest {
    return {
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
      path: isSet(object.path) ? globalThis.String(object.path) : "",
    };
  },

  toJSON(message: SubscribeToFileRequest): unknown {
    const obj: any = {};
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata);
    }
    if (message.path !== "") {
      obj.path = message.path;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeToFileRequest>, I>>(base?: I): SubscribeToFileRequest {
    return SubscribeToFileRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeToFileRequest>, I>>(object: I): SubscribeToFileRequest {
    const message = createBaseSubscribeToFileRequest();
    message.metadata = (object.metadata !== undefined && object.metadata !== null)
      ? Metadata.fromPartial(object.metadata)
      : undefined;
    message.path = object.path ?? "";
    return message;
  },
};

function createBaseFileChangeEvent(): FileChangeEvent {
  return { path: "", type: 0, content: "" };
}

export const FileChangeEvent: MessageFns<FileChangeEvent> = {
  encode(message: FileChangeEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.path !== "") {
      writer.uint32(10).string(message.path);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    if (message.content !== "") {
      writer.uint32(26).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FileChangeEvent {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFileChangeEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.path = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FileChangeEvent {
    return {
      path: isSet(object.path) ? globalThis.String(object.path) : "",
      type: isSet(object.type) ? fileChangeEvent_ChangeTypeFromJSON(object.type) : 0,
      content: isSet(object.content) ? globalThis.String(object.content) : "",
    };
  },

  toJSON(message: FileChangeEvent): unknown {
    const obj: any = {};
    if (message.path !== "") {
      obj.path = message.path;
    }
    if (message.type !== 0) {
      obj.type = fileChangeEvent_ChangeTypeToJSON(message.type);
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FileChangeEvent>, I>>(base?: I): FileChangeEvent {
    return FileChangeEvent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FileChangeEvent>, I>>(object: I): FileChangeEvent {
    const message = createBaseFileChangeEvent();
    message.path = object.path ?? "";
    message.type = object.type ?? 0;
    message.content = object.content ?? "";
    return message;
  },
};

/** WatchService provides methods for watching files in the IDE */
export type WatchServiceDefinition = typeof WatchServiceDefinition;
export const WatchServiceDefinition = {
  name: "WatchService",
  fullName: "host.WatchService",
  methods: {
    /** Subscribe to file changes */
    subscribeToFile: {
      name: "subscribeToFile",
      requestType: SubscribeToFileRequest,
      requestStream: false,
      responseType: FileChangeEvent,
      responseStream: true,
      options: {},
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
