syntax = "proto3";

package cline;
option java_package = "bot.cline.proto";
option java_multiple_files = true;

import "common.proto";

// Service for file-related operations
service FileService {
  // Copies text to clipboard
  rpc copyToClipboard(StringRequest) returns (Empty);

  // Opens a file in the editor
  rpc openFile(StringRequest) returns (Empty);
 
  // Opens an image in the system viewer
  rpc openImage(StringRequest) returns (Empty);
  
  // Opens a mention (file, path, git commit, problem, terminal, or URL)
  rpc openMention(StringRequest) returns (Empty);

  // Deletes a rule file from either global or workspace rules directory
  rpc deleteRuleFile(RuleFileRequest) returns (RuleFile);

  // Creates a rule file from either global or workspace rules directory
  rpc createRuleFile(RuleFileRequest) returns (RuleFile);
  
  // Search git commits in the workspace
  rpc searchCommits(StringRequest) returns (GitCommits);

  // Select images from the file system and return as data URLs
  rpc selectImages(EmptyRequest) returns (StringArray);

  // Select images and other files from the file system and returns as data URLs & paths respectively
  rpc selectFiles(BooleanRequest) returns (StringArrays);
  
  // Convert URIs to workspace-relative paths
  rpc getRelativePaths(RelativePathsRequest) returns (RelativePaths);

  // Search for files in the workspace with fuzzy matching
  rpc searchFiles(FileSearchRequest) returns (FileSearchResults);
  
  // Toggle a Cline rule (enable or disable)
  rpc toggleClineRule(ToggleClineRuleRequest) returns (ToggleClineRules);

  // Toggle a Cursor rule (enable or disable)
  rpc toggleCursorRule(ToggleCursorRuleRequest) returns (ClineRulesToggles);
  
  // Toggle a Windsurf rule (enable or disable)
  rpc toggleWindsurfRule(ToggleWindsurfRuleRequest) returns (ClineRulesToggles);
  
  // Refreshes all rule toggles (Cline, External, and Workflows)
  rpc refreshRules(EmptyRequest) returns (RefreshedRules);

  // Opens a task's conversation history file on disk
  rpc openTaskHistory(StringRequest) returns (Empty);
  
  // Toggles a workflow on or off
  rpc toggleWorkflow(ToggleWorkflowRequest) returns (ClineRulesToggles);

  // Subscribe to workspace file updates
  rpc subscribeToWorkspaceUpdates(EmptyRequest) returns (stream StringArray);
}

// Response for refreshRules operation
message RefreshedRules {
  ClineRulesToggles global_cline_rules_toggles = 1;
  ClineRulesToggles local_cline_rules_toggles = 2;
  ClineRulesToggles local_cursor_rules_toggles = 3;
  ClineRulesToggles local_windsurf_rules_toggles = 4;
  ClineRulesToggles local_workflow_toggles = 5;
  ClineRulesToggles global_workflow_toggles = 6;
}

// Request to toggle a Windsurf rule
message ToggleWindsurfRuleRequest {
  Metadata metadata = 1;
  string rule_path = 2;   // Path to the rule file
  bool enabled = 3;       // Whether to enable or disable the rule
}

// Request to convert a list of URIs to relative paths
message RelativePathsRequest {
  Metadata metadata = 1;
  repeated string uris = 2;
}

// Response containing the converted relative paths
message RelativePaths {
  repeated string paths = 1;
}

// Request for file search operations
message FileSearchRequest {
  Metadata metadata = 1;
  string query = 2;                  // Search query string
  optional string mentions_request_id = 3;  // Optional request ID for tracking requests
  optional int32 limit = 4;          // Optional limit for results (default: 20)
}

// Result for file search operations
message FileSearchResults {
  repeated FileInfo results = 1;     // Array of file/folder results
  optional string mentions_request_id = 2;  // Echo of the request ID for tracking
}

// File information structure for search results
message FileInfo {
  string path = 1;                   // Relative path from workspace root
  string type = 2;                   // "file" or "folder"
  optional string label = 3;         // Display name (usually basename)
}

// Response for searchCommits
message GitCommits {
  repeated GitCommit commits = 1;
}

// Represents a Git commit
message GitCommit {
  string hash = 1;
  string short_hash = 2;
  string subject = 3;
  string author = 4;
  string date = 5;
}

// Unified request for all rule file operations
message RuleFileRequest {
  Metadata metadata = 1;
  bool is_global = 2;            // Common field for all operations
  optional string rule_path = 3; // Path field for deleteRuleFile (optional)
  optional string filename = 4;  // Filename field for createRuleFile (optional)
  optional string type = 5;      // Type of the file to create (optional)
}

// Result for rule file operations with meaningful data only
message RuleFile {
  string file_path = 1;     // Path to the rule file
  string display_name = 2;  // Filename for display purposes
  bool already_exists = 3;  // For createRuleFile, indicates if file already existed
}

// Request to toggle a Cline rule
message ToggleClineRuleRequest {
  Metadata metadata = 1;
  bool is_global = 2;     // Whether this is a global rule or workspace rule
  string rule_path = 3;   // Path to the rule file
  bool enabled = 4;       // Whether to enable or disable the rule
}

// Maps from filepath to enabled/disabled status, matching app's ClineRulesToggles type
message ClineRulesToggles {
  map<string, bool> toggles = 1;
}

// Response for toggleClineRule operation
message ToggleClineRules {
  ClineRulesToggles global_cline_rules_toggles = 1;
  ClineRulesToggles local_cline_rules_toggles = 2;
}

// Request to toggle a Cursor rule
message ToggleCursorRuleRequest {
  Metadata metadata = 1;
  string rule_path = 2;   // Path to the rule file
  bool enabled = 3;       // Whether to enable or disable the rule
}

// Request to toggle a workflow on or off
message ToggleWorkflowRequest {
  Metadata metadata = 1;
  string workflow_path = 2;
  bool enabled = 3;
  bool is_global = 4;
}
