// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js

import { createServiceRegistry, ServiceMethodHandler, StreamingMethodHandler } from "../grpc-service"
import { StreamingResponseHandler } from "../grpc-handler"
import { registerAllMethods } from "./methods"

// Create ui service registry
const uiService = createServiceRegistry("ui")

// Export the method handler types and registration function
export type UiMethodHandler = ServiceMethodHandler
export type UiStreamingMethodHandler = StreamingMethodHandler
export const registerMethod = uiService.registerMethod

// Export the request handlers
export const handleUiServiceRequest = uiService.handleRequest
export const handleUiServiceStreamingRequest = uiService.handleStreamingRequest
export const isStreamingMethod = uiService.isStreamingMethod

// Register all ui methods
registerAllMethods()