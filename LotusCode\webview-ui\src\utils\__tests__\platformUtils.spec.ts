import { describe, it, expect } from "vitest"
import { detectMeta<PERSON>eyChar } from "../platformUtils"

describe("detectMeta<PERSON>ey<PERSON><PERSON>", () => {
	it("should return ⌘ Command for darwin platform", () => {
		const result = detectMeta<PERSON>eyChar("darwin")
		expect(result).toBe("CMD")
	})

	it("should return ⊞ Win for win32 platform", () => {
		const result = detectMeta<PERSON><PERSON><PERSON>har("win32")
		expect(result).toBe("Win")
	})

	it("should return Alt for linux platform", () => {
		const result = detectMeta<PERSON>eyChar("linux")
		expect(result).toBe("Alt")
	})

	it("should return generic CMD for unknown platform", () => {
		const result = detectMeta<PERSON>eyChar("somethingelse")
		expect(result).toBe("CMD")
	})
})
