// GENERATED CODE -- DO NOT EDIT!
// Generated by generate-server-setup.mjs
import * as grpc from "@grpc/grpc-js"
import { cline } from "@generated/grpc-js"
import { Controller } from "@core/controller"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rap<PERSON>, GrpcStreamingResponseHandlerWrapper } from "@/standalone/grpc-types"

// Account Service
import { accountLoginClicked } from "@core/controller/account/accountLoginClicked"
import { accountLogoutClicked } from "@core/controller/account/accountLogoutClicked"
import { subscribeToAuthCallback } from "@core/controller/account/subscribeToAuthCallback"
import { authStateChanged } from "@core/controller/account/authStateChanged"

// Browser Service
import { getBrowserConnectionInfo } from "@core/controller/browser/getBrowserConnectionInfo"
import { testBrowserConnection } from "@core/controller/browser/testBrowserConnection"
import { discoverBrowser } from "@core/controller/browser/discoverBrowser"
import { getDetectedChromePath } from "@core/controller/browser/getDetectedChromePath"
import { updateBrowserSettings } from "@core/controller/browser/updateBrowserSettings"
import { relaunchChromeDebugMode } from "@core/controller/browser/relaunchChromeDebugMode"

// Checkpoints Service
import { checkpointDiff } from "@core/controller/checkpoints/checkpointDiff"
import { checkpointRestore } from "@core/controller/checkpoints/checkpointRestore"

// File Service
import { copyToClipboard } from "@core/controller/file/copyToClipboard"
import { openFile } from "@core/controller/file/openFile"
import { openImage } from "@core/controller/file/openImage"
import { openMention } from "@core/controller/file/openMention"
import { deleteRuleFile } from "@core/controller/file/deleteRuleFile"
import { createRuleFile } from "@core/controller/file/createRuleFile"
import { searchCommits } from "@core/controller/file/searchCommits"
import { selectImages } from "@core/controller/file/selectImages"
import { selectFiles } from "@core/controller/file/selectFiles"
import { getRelativePaths } from "@core/controller/file/getRelativePaths"
import { searchFiles } from "@core/controller/file/searchFiles"
import { toggleClineRule } from "@core/controller/file/toggleClineRule"
import { toggleCursorRule } from "@core/controller/file/toggleCursorRule"
import { toggleWindsurfRule } from "@core/controller/file/toggleWindsurfRule"
import { refreshRules } from "@core/controller/file/refreshRules"
import { openTaskHistory } from "@core/controller/file/openTaskHistory"
import { toggleWorkflow } from "@core/controller/file/toggleWorkflow"
import { subscribeToWorkspaceUpdates } from "@core/controller/file/subscribeToWorkspaceUpdates"

// Mcp Service
import { toggleMcpServer } from "@core/controller/mcp/toggleMcpServer"
import { updateMcpTimeout } from "@core/controller/mcp/updateMcpTimeout"
import { addRemoteMcpServer } from "@core/controller/mcp/addRemoteMcpServer"
import { downloadMcp } from "@core/controller/mcp/downloadMcp"
import { restartMcpServer } from "@core/controller/mcp/restartMcpServer"
import { deleteMcpServer } from "@core/controller/mcp/deleteMcpServer"
import { toggleToolAutoApprove } from "@core/controller/mcp/toggleToolAutoApprove"
import { refreshMcpMarketplace } from "@core/controller/mcp/refreshMcpMarketplace"
import { openMcpSettings } from "@core/controller/mcp/openMcpSettings"
import { subscribeToMcpMarketplaceCatalog } from "@core/controller/mcp/subscribeToMcpMarketplaceCatalog"
import { getLatestMcpServers } from "@core/controller/mcp/getLatestMcpServers"
import { subscribeToMcpServers } from "@core/controller/mcp/subscribeToMcpServers"

// Models Service
import { getOllamaModels } from "@core/controller/models/getOllamaModels"
import { getLmStudioModels } from "@core/controller/models/getLmStudioModels"
import { getVsCodeLmModels } from "@core/controller/models/getVsCodeLmModels"
import { refreshOpenRouterModels } from "@core/controller/models/refreshOpenRouterModels"
import { refreshOpenAiModels } from "@core/controller/models/refreshOpenAiModels"
import { refreshRequestyModels } from "@core/controller/models/refreshRequestyModels"
import { subscribeToOpenRouterModels } from "@core/controller/models/subscribeToOpenRouterModels"
import { updateApiConfigurationProto } from "@core/controller/models/updateApiConfigurationProto"

// Slash Service
import { reportBug } from "@core/controller/slash/reportBug"
import { condense } from "@core/controller/slash/condense"

// State Service
import { getLatestState } from "@core/controller/state/getLatestState"
import { updateTerminalConnectionTimeout } from "@core/controller/state/updateTerminalConnectionTimeout"
import { updateTerminalReuseEnabled } from "@core/controller/state/updateTerminalReuseEnabled"
import { updateDefaultTerminalProfile } from "@core/controller/state/updateDefaultTerminalProfile"
import { getAvailableTerminalProfiles } from "@core/controller/state/getAvailableTerminalProfiles"
import { subscribeToState } from "@core/controller/state/subscribeToState"
import { toggleFavoriteModel } from "@core/controller/state/toggleFavoriteModel"
import { resetState } from "@core/controller/state/resetState"
import { togglePlanActMode } from "@core/controller/state/togglePlanActMode"
import { updateAutoApprovalSettings } from "@core/controller/state/updateAutoApprovalSettings"
import { updateSettings } from "@core/controller/state/updateSettings"

// Task Service
import { cancelTask } from "@core/controller/task/cancelTask"
import { clearTask } from "@core/controller/task/clearTask"
import { getTotalTasksSize } from "@core/controller/task/getTotalTasksSize"
import { deleteTasksWithIds } from "@core/controller/task/deleteTasksWithIds"
import { newTask } from "@core/controller/task/newTask"
import { showTaskWithId } from "@core/controller/task/showTaskWithId"
import { exportTaskWithId } from "@core/controller/task/exportTaskWithId"
import { toggleTaskFavorite } from "@core/controller/task/toggleTaskFavorite"
import { deleteNonFavoritedTasks } from "@core/controller/task/deleteNonFavoritedTasks"
import { getTaskHistory } from "@core/controller/task/getTaskHistory"
import { askResponse } from "@core/controller/task/askResponse"
import { taskFeedback } from "@core/controller/task/taskFeedback"
import { taskCompletionViewChanges } from "@core/controller/task/taskCompletionViewChanges"
import { executeQuickWin } from "@core/controller/task/executeQuickWin"

// Ui Service
import { scrollToSettings } from "@core/controller/ui/scrollToSettings"
import { onDidShowAnnouncement } from "@core/controller/ui/onDidShowAnnouncement"
import { subscribeToAddToInput } from "@core/controller/ui/subscribeToAddToInput"
import { subscribeToMcpButtonClicked } from "@core/controller/ui/subscribeToMcpButtonClicked"
import { subscribeToHistoryButtonClicked } from "@core/controller/ui/subscribeToHistoryButtonClicked"
import { subscribeToChatButtonClicked } from "@core/controller/ui/subscribeToChatButtonClicked"
import { subscribeToAccountButtonClicked } from "@core/controller/ui/subscribeToAccountButtonClicked"
import { subscribeToSettingsButtonClicked } from "@core/controller/ui/subscribeToSettingsButtonClicked"
import { subscribeToPartialMessage } from "@core/controller/ui/subscribeToPartialMessage"
import { subscribeToTheme } from "@core/controller/ui/subscribeToTheme"
import { initializeWebview } from "@core/controller/ui/initializeWebview"
import { subscribeToRelinquishControl } from "@core/controller/ui/subscribeToRelinquishControl"
import { subscribeToFocusChatInput } from "@core/controller/ui/subscribeToFocusChatInput"
import { subscribeToDidBecomeVisible } from "@core/controller/ui/subscribeToDidBecomeVisible"
import { getWebviewHtml } from "@core/controller/ui/getWebviewHtml"

// Web Service
import { checkIsImageUrl } from "@core/controller/web/checkIsImageUrl"
import { fetchOpenGraphData } from "@core/controller/web/fetchOpenGraphData"
import { openInBrowser } from "@core/controller/web/openInBrowser"

export function addProtobusServices(
	server: grpc.Server,
	controller: Controller,
	wrapper: GrpcHandlerWrapper,
	wrapStreamingResponse: GrpcStreamingResponseHandlerWrapper,
): void {
    // Account Service
    server.addService(cline.AccountServiceService, {
         accountLoginClicked: wrapper<cline.EmptyRequest,cline.String>(accountLoginClicked, controller),
         accountLogoutClicked: wrapper<cline.EmptyRequest,cline.Empty>(accountLogoutClicked, controller),
        subscribeToAuthCallback: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToAuthCallback, controller),
         authStateChanged: wrapper<cline.AuthStateChangedRequest,cline.AuthStateChanged>(authStateChanged, controller),
    });

    // Browser Service
    server.addService(cline.BrowserServiceService, {
         getBrowserConnectionInfo: wrapper<cline.EmptyRequest,cline.BrowserConnectionInfo>(getBrowserConnectionInfo, controller),
         testBrowserConnection: wrapper<cline.StringRequest,cline.BrowserConnection>(testBrowserConnection, controller),
         discoverBrowser: wrapper<cline.EmptyRequest,cline.BrowserConnection>(discoverBrowser, controller),
         getDetectedChromePath: wrapper<cline.EmptyRequest,cline.ChromePath>(getDetectedChromePath, controller),
         updateBrowserSettings: wrapper<cline.UpdateBrowserSettingsRequest,cline.Boolean>(updateBrowserSettings, controller),
         relaunchChromeDebugMode: wrapper<cline.EmptyRequest,cline.String>(relaunchChromeDebugMode, controller),
    });

    // Checkpoints Service
    server.addService(cline.CheckpointsServiceService, {
         checkpointDiff: wrapper<cline.Int64Request,cline.Empty>(checkpointDiff, controller),
         checkpointRestore: wrapper<cline.CheckpointRestoreRequest,cline.Empty>(checkpointRestore, controller),
    });

    // File Service
    server.addService(cline.FileServiceService, {
         copyToClipboard: wrapper<cline.StringRequest,cline.Empty>(copyToClipboard, controller),
         openFile: wrapper<cline.StringRequest,cline.Empty>(openFile, controller),
         openImage: wrapper<cline.StringRequest,cline.Empty>(openImage, controller),
         openMention: wrapper<cline.StringRequest,cline.Empty>(openMention, controller),
         deleteRuleFile: wrapper<cline.RuleFileRequest,cline.RuleFile>(deleteRuleFile, controller),
         createRuleFile: wrapper<cline.RuleFileRequest,cline.RuleFile>(createRuleFile, controller),
         searchCommits: wrapper<cline.StringRequest,cline.GitCommits>(searchCommits, controller),
         selectImages: wrapper<cline.EmptyRequest,cline.StringArray>(selectImages, controller),
         selectFiles: wrapper<cline.BooleanRequest,cline.StringArrays>(selectFiles, controller),
         getRelativePaths: wrapper<cline.RelativePathsRequest,cline.RelativePaths>(getRelativePaths, controller),
         searchFiles: wrapper<cline.FileSearchRequest,cline.FileSearchResults>(searchFiles, controller),
         toggleClineRule: wrapper<cline.ToggleClineRuleRequest,cline.ToggleClineRules>(toggleClineRule, controller),
         toggleCursorRule: wrapper<cline.ToggleCursorRuleRequest,cline.ClineRulesToggles>(toggleCursorRule, controller),
         toggleWindsurfRule: wrapper<cline.ToggleWindsurfRuleRequest,cline.ClineRulesToggles>(toggleWindsurfRule, controller),
         refreshRules: wrapper<cline.EmptyRequest,cline.RefreshedRules>(refreshRules, controller),
         openTaskHistory: wrapper<cline.StringRequest,cline.Empty>(openTaskHistory, controller),
         toggleWorkflow: wrapper<cline.ToggleWorkflowRequest,cline.ClineRulesToggles>(toggleWorkflow, controller),
        subscribeToWorkspaceUpdates: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToWorkspaceUpdates, controller),
    });

    // Mcp Service
    server.addService(cline.McpServiceService, {
         toggleMcpServer: wrapper<cline.ToggleMcpServerRequest,cline.McpServers>(toggleMcpServer, controller),
         updateMcpTimeout: wrapper<cline.UpdateMcpTimeoutRequest,cline.McpServers>(updateMcpTimeout, controller),
         addRemoteMcpServer: wrapper<cline.AddRemoteMcpServerRequest,cline.McpServers>(addRemoteMcpServer, controller),
         downloadMcp: wrapper<cline.StringRequest,cline.Empty>(downloadMcp, controller),
         restartMcpServer: wrapper<cline.StringRequest,cline.McpServers>(restartMcpServer, controller),
         deleteMcpServer: wrapper<cline.StringRequest,cline.McpServers>(deleteMcpServer, controller),
         toggleToolAutoApprove: wrapper<cline.ToggleToolAutoApproveRequest,cline.McpServers>(toggleToolAutoApprove, controller),
         refreshMcpMarketplace: wrapper<cline.EmptyRequest,cline.McpMarketplaceCatalog>(refreshMcpMarketplace, controller),
         openMcpSettings: wrapper<cline.EmptyRequest,cline.Empty>(openMcpSettings, controller),
        subscribeToMcpMarketplaceCatalog: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToMcpMarketplaceCatalog, controller),
         getLatestMcpServers: wrapper<cline.Empty,cline.McpServers>(getLatestMcpServers, controller),
        subscribeToMcpServers: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToMcpServers, controller),
    });

    // Models Service
    server.addService(cline.ModelsServiceService, {
         getOllamaModels: wrapper<cline.StringRequest,cline.StringArray>(getOllamaModels, controller),
         getLmStudioModels: wrapper<cline.StringRequest,cline.StringArray>(getLmStudioModels, controller),
         getVsCodeLmModels: wrapper<cline.EmptyRequest,cline.VsCodeLmModelsArray>(getVsCodeLmModels, controller),
         refreshOpenRouterModels: wrapper<cline.EmptyRequest,cline.OpenRouterCompatibleModelInfo>(refreshOpenRouterModels, controller),
         refreshOpenAiModels: wrapper<cline.OpenAiModelsRequest,cline.StringArray>(refreshOpenAiModels, controller),
         refreshRequestyModels: wrapper<cline.EmptyRequest,cline.OpenRouterCompatibleModelInfo>(refreshRequestyModels, controller),
        subscribeToOpenRouterModels: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToOpenRouterModels, controller),
         updateApiConfigurationProto: wrapper<cline.UpdateApiConfigurationRequest,cline.Empty>(updateApiConfigurationProto, controller),
    });

    // Slash Service
    server.addService(cline.SlashServiceService, {
         reportBug: wrapper<cline.StringRequest,cline.Empty>(reportBug, controller),
         condense: wrapper<cline.StringRequest,cline.Empty>(condense, controller),
    });

    // State Service
    server.addService(cline.StateServiceService, {
         getLatestState: wrapper<cline.EmptyRequest,cline.State>(getLatestState, controller),
         updateTerminalConnectionTimeout: wrapper<cline.Int64Request,cline.Int64>(updateTerminalConnectionTimeout, controller),
         updateTerminalReuseEnabled: wrapper<cline.BooleanRequest,cline.Empty>(updateTerminalReuseEnabled, controller),
         updateDefaultTerminalProfile: wrapper<cline.StringRequest,cline.TerminalProfileUpdateResponse>(updateDefaultTerminalProfile, controller),
         getAvailableTerminalProfiles: wrapper<cline.EmptyRequest,cline.TerminalProfiles>(getAvailableTerminalProfiles, controller),
        subscribeToState: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToState, controller),
         toggleFavoriteModel: wrapper<cline.StringRequest,cline.Empty>(toggleFavoriteModel, controller),
         resetState: wrapper<cline.ResetStateRequest,cline.Empty>(resetState, controller),
         togglePlanActMode: wrapper<cline.TogglePlanActModeRequest,cline.Boolean>(togglePlanActMode, controller),
         updateAutoApprovalSettings: wrapper<cline.AutoApprovalSettingsRequest,cline.Empty>(updateAutoApprovalSettings, controller),
         updateSettings: wrapper<cline.UpdateSettingsRequest,cline.Empty>(updateSettings, controller),
    });

    // Task Service
    server.addService(cline.TaskServiceService, {
         cancelTask: wrapper<cline.EmptyRequest,cline.Empty>(cancelTask, controller),
         clearTask: wrapper<cline.EmptyRequest,cline.Empty>(clearTask, controller),
         getTotalTasksSize: wrapper<cline.EmptyRequest,cline.Int64>(getTotalTasksSize, controller),
         deleteTasksWithIds: wrapper<cline.StringArrayRequest,cline.Empty>(deleteTasksWithIds, controller),
         newTask: wrapper<cline.NewTaskRequest,cline.Empty>(newTask, controller),
         showTaskWithId: wrapper<cline.StringRequest,cline.TaskResponse>(showTaskWithId, controller),
         exportTaskWithId: wrapper<cline.StringRequest,cline.Empty>(exportTaskWithId, controller),
         toggleTaskFavorite: wrapper<cline.TaskFavoriteRequest,cline.Empty>(toggleTaskFavorite, controller),
         deleteNonFavoritedTasks: wrapper<cline.EmptyRequest,cline.DeleteNonFavoritedTasksResults>(deleteNonFavoritedTasks, controller),
         getTaskHistory: wrapper<cline.GetTaskHistoryRequest,cline.TaskHistoryArray>(getTaskHistory, controller),
         askResponse: wrapper<cline.AskResponseRequest,cline.Empty>(askResponse, controller),
         taskFeedback: wrapper<cline.StringRequest,cline.Empty>(taskFeedback, controller),
         taskCompletionViewChanges: wrapper<cline.Int64Request,cline.Empty>(taskCompletionViewChanges, controller),
         executeQuickWin: wrapper<cline.ExecuteQuickWinRequest,cline.Empty>(executeQuickWin, controller),
    });

    // Ui Service
    server.addService(cline.UiServiceService, {
         scrollToSettings: wrapper<cline.StringRequest,cline.KeyValuePair>(scrollToSettings, controller),
         onDidShowAnnouncement: wrapper<cline.EmptyRequest,cline.Boolean>(onDidShowAnnouncement, controller),
        subscribeToAddToInput: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToAddToInput, controller),
        subscribeToMcpButtonClicked: wrapStreamingResponse<cline.WebviewProviderTypeRequest,void>(subscribeToMcpButtonClicked, controller),
        subscribeToHistoryButtonClicked: wrapStreamingResponse<cline.WebviewProviderTypeRequest,void>(subscribeToHistoryButtonClicked, controller),
        subscribeToChatButtonClicked: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToChatButtonClicked, controller),
        subscribeToAccountButtonClicked: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToAccountButtonClicked, controller),
        subscribeToSettingsButtonClicked: wrapStreamingResponse<cline.WebviewProviderTypeRequest,void>(subscribeToSettingsButtonClicked, controller),
        subscribeToPartialMessage: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToPartialMessage, controller),
        subscribeToTheme: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToTheme, controller),
         initializeWebview: wrapper<cline.EmptyRequest,cline.Empty>(initializeWebview, controller),
        subscribeToRelinquishControl: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToRelinquishControl, controller),
        subscribeToFocusChatInput: wrapStreamingResponse<cline.StringRequest,void>(subscribeToFocusChatInput, controller),
        subscribeToDidBecomeVisible: wrapStreamingResponse<cline.EmptyRequest,void>(subscribeToDidBecomeVisible, controller),
         getWebviewHtml: wrapper<cline.EmptyRequest,cline.String>(getWebviewHtml, controller),
    });

    // Web Service
    server.addService(cline.WebServiceService, {
         checkIsImageUrl: wrapper<cline.StringRequest,cline.IsImageUrl>(checkIsImageUrl, controller),
         fetchOpenGraphData: wrapper<cline.StringRequest,cline.OpenGraphData>(fetchOpenGraphData, controller),
         openInBrowser: wrapper<cline.StringRequest,cline.Empty>(openInBrowser, controller),
    });

}
